#!/usr/bin/env pwsh
# VelociTun Driver Build Script
# Usage: .\build-driver.ps1 [-Clean] [-Debug] [-Platform x64|Win32|ARM64]

param(
    [switch]$Clean,
    [switch]$Debug,
    [ValidateSet("x64", "Win32", "ARM64")]
    [string]$Platform = "x64",
    [switch]$Help
)

if ($Help) {
    Write-Host @"
VelociTun Driver Build Script

Usage: .\build-driver.ps1 [OPTIONS]

Options:
    -Clean      Clean build artifacts before building
    -Debug      Build debug version instead of release
    -Platform   Target platform (x64, Win32, ARM64) [default: x64]
    -Help       Show this help message

Examples:
    .\build-driver.ps1                    # Build release x64 driver
    .\build-driver.ps1 -Debug             # Build debug x64 driver
    .\build-driver.ps1 -Clean -Platform x64  # Clean and build release x64 driver
"@
    exit 0
}

# Configuration
$Configuration = if ($Debug) { "Debug" } else { "Release" }
$ProjectFile = "driver\velocitun.vcxproj"
$ResourcesDir = "resources"

Write-Host "VelociTun Driver Build Script" -ForegroundColor Cyan
Write-Host "Configuration: $Configuration" -ForegroundColor Yellow
Write-Host "Platform: $Platform" -ForegroundColor Yellow

# Find MSBuild
$MSBuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
)

$MSBuildPath = $null
foreach ($path in $MSBuildPaths) {
    if (Test-Path $path) {
        $MSBuildPath = $path
        Write-Host "Found MSBuild: $MSBuildPath" -ForegroundColor Green
        break
    }
}

if (-not $MSBuildPath) {
    Write-Error "MSBuild not found. Please install Visual Studio with C++ workload and Windows Driver Kit."
    exit 1
}

# Check if project file exists
if (-not (Test-Path $ProjectFile)) {
    Write-Error "Project file not found: $ProjectFile"
    exit 1
}

# Clean if requested
if ($Clean) {
    Write-Host "Cleaning build artifacts..." -ForegroundColor Yellow
    
    $CleanPaths = @(
        "driver\x64",
        "driver\Win32",
        "driver\ARM64",
        "driver\Debug",
        "driver\Release"
    )
    
    foreach ($path in $CleanPaths) {
        if (Test-Path $path) {
            Remove-Item $path -Recurse -Force
            Write-Host "Removed: $path" -ForegroundColor Gray
        }
    }
}

# Build the driver
Write-Host "Building driver..." -ForegroundColor Yellow

$BuildArgs = @(
    $ProjectFile,
    "/p:Configuration=$Configuration",
    "/p:Platform=$Platform",
    "/p:TargetVersion=Windows10",
    "/p:EnableInfVerif=false",
    "/p:SignMode=Off",
    "/verbosity:minimal"
)

Write-Host "Executing: $MSBuildPath $($BuildArgs -join ' ')" -ForegroundColor Gray

$Process = Start-Process -FilePath $MSBuildPath -ArgumentList $BuildArgs -Wait -PassThru -NoNewWindow

if ($Process.ExitCode -eq 0) {
    Write-Host "Driver built successfully!" -ForegroundColor Green
    
    # Create resources directory
    if (-not (Test-Path $ResourcesDir)) {
        New-Item -ItemType Directory -Path $ResourcesDir -Force | Out-Null
        Write-Host "Created resources directory" -ForegroundColor Gray
    }
    
    # Copy driver files to resources
    $DriverFiles = @(
        @{
            Source = "driver\$Platform\$Configuration\velocitun.sys"
            Target = "$ResourcesDir\velocitun-$($Platform.ToLower()).sys"
        },
        @{
            Source = "driver\$Platform\$Configuration\velocitun\velocitun.sys"
            Target = "$ResourcesDir\velocitun-$($Platform.ToLower()).sys"
        },
        @{
            Source = "driver\$Platform\$Configuration\velocitun\velocitun.inf"
            Target = "$ResourcesDir\velocitun.inf"
        },
        @{
            Source = "driver\$Platform\$Configuration\velocitun\velocitun.cat"
            Target = "$ResourcesDir\velocitun.cat"
        }
    )
    
    $CopiedFiles = 0
    foreach ($file in $DriverFiles) {
        if (Test-Path $file.Source) {
            Copy-Item $file.Source $file.Target -Force
            $size = (Get-Item $file.Target).Length
            Write-Host "Copied: $($file.Source) -> $($file.Target) ($size bytes)" -ForegroundColor Green
            $CopiedFiles++
        }
    }
    
    if ($CopiedFiles -eq 0) {
        Write-Warning "No driver files were copied. Check build output paths."
    }
    
    # Show build summary
    Write-Host "`nBuild Summary:" -ForegroundColor Cyan
    Write-Host "  Configuration: $Configuration" -ForegroundColor White
    Write-Host "  Platform: $Platform" -ForegroundColor White
    Write-Host "  Files copied: $CopiedFiles" -ForegroundColor White
    
} else {
    Write-Error "Driver build failed with exit code: $($Process.ExitCode)"
    exit $Process.ExitCode
}
