use std::env;
use std::path::PathBuf;
use std::process::Command;

// VelociTun build script - generates functional driver resource files at build time

fn main() {
    println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed=resources/");
    println!("cargo:rerun-if-changed=driver/");

    let target = env::var("TARGET").unwrap();

    if target.contains("windows") {
        // Link with Windows libraries
        println!("cargo:rustc-link-lib=kernel32");
        println!("cargo:rustc-link-lib=advapi32");
        println!("cargo:rustc-link-lib=setupapi");
        println!("cargo:rustc-link-lib=newdev");
        println!("cargo:rustc-link-lib=iphlpapi");
        println!("cargo:rustc-link-lib=ws2_32");
        println!("cargo:rustc-link-lib=user32");
        println!("cargo:rustc-link-lib=version");

        // Set up WDF library linking if available
        if let Ok(wdk_path) = env::var("WDK_PATH") {
            let wdf_lib_path = format!("{}/lib/wdf/kmdf/x64/1.15", wdk_path);
            println!("cargo:rustc-link-search=native={}", wdf_lib_path);
            println!("cargo:rustc-link-lib=wdf01000");
        }

        // Try to build the driver if WDK is available
        build_driver_if_possible();
    }
}

fn build_driver_if_possible() {
    // Check if we have MSBuild available
    if let Ok(msbuild_path) = find_msbuild() {
        // Try to build the kernel driver
        let driver_project = PathBuf::from("driver/velocitun.vcxproj");
        if driver_project.exists() {
            pri
            println!("cargo:warning=Attempting to build VelociTun kernel driver...");

            let output = Command::new(&msbuild_path)
                .arg(&driver_project)
                .arg("/p:Configuration=Release")
                .arg("/p:Platform=x64")
                .arg("/p:TargetVersion=Windows10")
                .arg("/p:EnableInfVerif=false") // Skip INF verification
                .arg("/p:SignMode=Off") // Skip signing for development
                .arg("/verbosity:minimal") // Reduce output noise
                .output();

            match output {
                Ok(result) => {
                    if result.status.success() {
                        println!("cargo:warning=VelociTun kernel driver built successfully!");

                        // Copy the built driver files to resources
                        copy_driver_files_to_resources();

                        // Set environment variable to indicate driver was built
                        println!("cargo:rustc-env=VELOCITUN_DRIVER_BUILT=1");
                    } else {
                        let stderr = String::from_utf8_lossy(&result.stdout);
                        println!(
                            "cargo:warning=Kernel driver build failed (expected without WDK): {}",
                            stderr
                        );
                    }
                }
                Err(e) => {
                    println!(
                        "cargo:warning=Failed to execute MSBuild for kernel driver: {}",
                        e
                    );
                }
            }
        } else {
            println!(
                "cargo:warning=Driver project file not found: {:?}",
                driver_project
            );
        }
    } else {
        println!("cargo:warning=MSBuild not found, skipping driver build");
    }
}

fn find_msbuild() -> Result<String, Box<dyn std::error::Error>> {
    // Try to find MSBuild in common locations
    let possible_paths = [
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
        r"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        r"C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
        r"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
        r"C:\Program Files (x86)\MSBuild\14.0\Bin\MSBuild.exe",
        r"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe",
    ];

    for path in &possible_paths {
        if PathBuf::from(path).exists() {
            return Ok(path.to_string());
        }
    }

    // Try using where command
    if let Ok(output) = Command::new("where").arg("msbuild").output() {
        if output.status.success() {
            let path = String::from_utf8_lossy(&output.stdout);
            let path = path.trim();
            if !path.is_empty() && PathBuf::from(path).exists() {
                return Ok(path.to_string());
            }
        }
    }

    Err("MSBuild not found".into())
}

fn copy_driver_files_to_resources() {
    use std::fs;

    // Ensure resources directory exists
    if let Err(e) = fs::create_dir_all("resources") {
        println!("cargo:warning=Failed to create resources directory: {}", e);
        return;
    }

    // Define source and target file mappings
    let file_mappings = [
        (
            "driver/x64/Release/velocitun/velocitun.sys",
            "resources/velocitun-amd64.sys",
        ),
        (
            "driver/x64/Release/velocitun/velocitun.inf",
            "resources/velocitun-amd64.inf",
        ),
        (
            "driver/x64/Release/velocitun/velocitun.cat",
            "resources/velocitun-amd64.cat",
        ),
    ];

    for (source, target) in &file_mappings {
        let source_path = PathBuf::from(source);
        let target_path = PathBuf::from(target);

        if source_path.exists() {
            if let Err(e) = fs::copy(&source_path, &target_path) {
                println!(
                    "cargo:warning=Failed to copy {} to {}: {}",
                    source, target, e
                );
            } else {
                println!("cargo:warning=Copied {} to {}", source, target);
            }
        }
    }
}
