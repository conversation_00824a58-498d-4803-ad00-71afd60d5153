/* SPDX-License-Identifier: GPL-2.0
 *
 * Copyright (C) 2024 VelociTun Team. All Rights Reserved.
 * Based on WireGuard Wintun driver.
 */

#include <ntifs.h>
#include <wdm.h>
#include <wdmsec.h>
#include <ndis.h>
#include <ntstrsafe.h>
#include "velocitun.h"
#include "undocumented.h"

#pragma warning(disable : 4100) /* unreferenced formal parameter */
#pragma warning(disable : 4200) /* nonstandard: zero-sized array in struct/union */
#pragma warning(disable : 4204) /* nonstandard: non-constant aggregate initializer */
#pragma warning(disable : 4221) /* nonstandard: cannot be initialized using address of automatic variable */
#pragma warning(disable : 6320) /* exception-filter expression is the constant EXCEPTION_EXECUTE_HANDLER */

#define NDIS_MINIPORT_VERSION_MIN ((NDIS_MINIPORT_MINIMUM_MAJOR_VERSION << 16) | NDIS_MINIPORT_MINIMUM_MINOR_VERSION)
#define NDIS_MINIPORT_VERSION_MAX ((NDIS_MINIPORT_MAJOR_VERSION << 16) | NDIS_MINIPORT_MINOR_VERSION)

#define VELOCITUN_VENDOR_NAME "VelociTun High-Speed Tunnel"
#define VELOCITUN_VENDOR_ID 0xFFFFFF01
#define VELOCITUN_LINK_SPEED 100000000000ULL /* 100gbps */

/* Memory alignment of packets and rings */
#define VELOCITUN_ALIGNMENT sizeof(ULONG)
#define VELOCITUN_ALIGN(Size) (((ULONG)(Size) + ((ULONG)VELOCITUN_ALIGNMENT - 1)) & ~((ULONG)VELOCITUN_ALIGNMENT - 1))
#define VELOCITUN_IS_ALIGNED(Size) (!((ULONG)(Size) & ((ULONG)VELOCITUN_ALIGNMENT - 1)))
/* Maximum IP packet size */
#define VELOCITUN_MAX_IP_PACKET_SIZE 0xFFFF
/* Maximum packet size */
#define VELOCITUN_MAX_PACKET_SIZE VELOCITUN_ALIGN(sizeof(VELOCITUN_PACKET) + VELOCITUN_MAX_IP_PACKET_SIZE)
/* Minimum ring capacity. */
#define VELOCITUN_MIN_RING_CAPACITY 0x20000 /* 128kiB */
/* Maximum ring capacity. */
#define VELOCITUN_MAX_RING_CAPACITY 0x4000000 /* 64MiB */
/* Calculates ring capacity */
#define VELOCITUN_RING_CAPACITY(Size) ((Size) - sizeof(VELOCITUN_RING) - (VELOCITUN_MAX_PACKET_SIZE - VELOCITUN_ALIGNMENT))
/* Calculates ring offset modulo capacity */
#define VELOCITUN_RING_WRAP(Value, Capacity) ((Value) & (Capacity - 1))

// HTONS and HTONL macros are now defined in velocitun.h

/* Register rings hosted by the client.
 * The lpInBuffer and nInBufferSize parameters of DeviceIoControl() must point to an VELOCITUN_REGISTER_RINGS struct.
 * Client must wait for this IOCTL to finish before adding packets to the ring. */
#define VELOCITUN_IOCTL_REGISTER_RINGS CTL_CODE(51821U, 0x970U, METHOD_BUFFERED, FILE_READ_DATA | FILE_WRITE_DATA)

// All structures are now defined in velocitun.h

static UINT NdisVersion;
static NDIS_HANDLE NdisMiniportDriverHandle;
static DRIVER_DISPATCH *NdisDispatchDeviceControl, *NdisDispatchClose, *NdisDispatchPnp;
ERESOURCE VelociTunDispatchCtxGuard, VelociTunDispatchDeviceListLock;
static RTL_STATIC_LIST_HEAD(VelociTunDispatchDeviceList);
/* Binary representation of O:SYD:P(A;;FA;;;SY)(A;;FA;;;BA)S:(ML;;NWNRNX;;;HI) */
static SECURITY_DESCRIPTOR *VelociTunDispatchSecurityDescriptor = (SECURITY_DESCRIPTOR *)(__declspec(align(8)) UCHAR[]){
    0x01, 0x00, 0x14, 0x90, 0x64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00,
    0x00, 0x02, 0x00, 0x1c, 0x00, 0x01, 0x00, 0x00, 0x00, 0x11, 0x00, 0x14, 0x00, 0x07, 0x00, 0x00, 0x00, 0x01, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x30, 0x00, 0x00, 0x02, 0x00, 0x34, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x14, 0x00, 0xff, 0x01, 0x1f, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x12, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x18, 0x00, 0xff, 0x01, 0x1f, 0x00, 0x01, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x20, 0x00, 0x00,
    0x00, 0x20, 0x02, 0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x12, 0x00, 0x00, 0x00};

_IRQL_requires_max_(DISPATCH_LEVEL) static VOID
    VelociTunIndicateStatus(_In_ NDIS_HANDLE MiniportAdapterHandle, _In_ NDIS_MEDIA_CONNECT_STATE MediaConnectState)
{
    NDIS_LINK_STATE State = {.Header = {.Type = NDIS_OBJECT_TYPE_DEFAULT,
                                        .Revision = NDIS_LINK_STATE_REVISION_1,
                                        .Size = NDIS_SIZEOF_LINK_STATE_REVISION_1},
                             .MediaConnectState = MediaConnectState,
                             .MediaDuplexState = MediaDuplexStateFull,
                             .XmitLinkSpeed = VELOCITUN_LINK_SPEED,
                             .RcvLinkSpeed = VELOCITUN_LINK_SPEED,
                             .PauseFunctions = NdisPauseFunctionsUnsupported};

    NDIS_STATUS_INDICATION Indication = {.Header = {.Type = NDIS_OBJECT_TYPE_STATUS_INDICATION,
                                                    .Revision = NDIS_STATUS_INDICATION_REVISION_1,
                                                    .Size = NDIS_SIZEOF_STATUS_INDICATION_REVISION_1},
                                         .SourceHandle = MiniportAdapterHandle,
                                         .StatusCode = NDIS_STATUS_LINK_STATE,
                                         .StatusBuffer = &State,
                                         .StatusBufferSize = sizeof(State)};

    NdisMIndicateStatusEx(MiniportAdapterHandle, &Indication);
}

/* Send: We should not modify NET_BUFFER_LIST_NEXT_NBL(Nbl) to prevent fragmented NBLs to separate.
 * Receive: NDIS may change NET_BUFFER_LIST_NEXT_NBL(Nbl) at will between the NdisMIndicateReceiveNetBufferLists() and
 * MINIPORT_RETURN_NET_BUFFER_LISTS calls. Therefore, we use our own ->Next pointer for book-keeping. */
#define NET_BUFFER_LIST_NEXT_NBL_EX(Nbl) (NET_BUFFER_LIST_MINIPORT_RESERVED(Nbl)[1])

static VOID
VelociTunNblSetOffsetAndMarkActive(_Inout_ NET_BUFFER_LIST *Nbl, _In_ ULONG Offset)
{
    ASSERT(VELOCITUN_IS_ALIGNED(Offset)); /* Alignment ensures bit 0 will be 0 (0=active, 1=completed). */
    NET_BUFFER_LIST_MINIPORT_RESERVED(Nbl)
    [0] = (VOID *)Offset;
}

static ULONG
VelociTunNblGetOffset(_In_ NET_BUFFER_LIST *Nbl)
{
    return (ULONG)((ULONG_PTR)(NET_BUFFER_LIST_MINIPORT_RESERVED(Nbl)[0]) & ~((ULONG_PTR)VELOCITUN_ALIGNMENT - 1));
}

static VOID
VelociTunNblMarkCompleted(_Inout_ NET_BUFFER_LIST *Nbl)
{
    *(ULONG_PTR *)&NET_BUFFER_LIST_MINIPORT_RESERVED(Nbl)[0] |= 1;
}

static BOOLEAN
VelociTunNblIsCompleted(_In_ NET_BUFFER_LIST *Nbl)
{
    return (ULONG_PTR)(NET_BUFFER_LIST_MINIPORT_RESERVED(Nbl)[0]) & 1;
}

MINIPORT_SEND_NET_BUFFER_LISTS VelociTunSendNetBufferLists;
_Use_decl_annotations_ VOID
VelociTunSendNetBufferLists(
    NDIS_HANDLE MiniportAdapterContext,
    NET_BUFFER_LIST *NetBufferLists,
    NDIS_PORT_NUMBER PortNumber,
    ULONG SendFlags)
{
    VELOCITUN_CTX *Ctx = (VELOCITUN_CTX *)MiniportAdapterContext;
    LONG64 SentPacketsCount = 0, SentPacketsSize = 0, ErrorPacketsCount = 0, DiscardedPacketsCount = 0;

    /* Measure NBLs. */
    ULONG PacketsCount = 0, RequiredRingSpace = 0;
    for (NET_BUFFER_LIST *Nbl = NetBufferLists; Nbl; Nbl = NET_BUFFER_LIST_NEXT_NBL(Nbl))
    {
        for (NET_BUFFER *Nb = NET_BUFFER_LIST_FIRST_NB(Nbl); Nb; Nb = NET_BUFFER_NEXT_NB(Nb))
        {
            PacketsCount++;
            UINT PacketSize = NET_BUFFER_DATA_LENGTH(Nb);
            if (PacketSize > VELOCITUN_MAX_IP_PACKET_SIZE)
                continue; /* The same condition holds down below, where we `goto skipPacket`. */
            RequiredRingSpace += VELOCITUN_ALIGN(sizeof(VELOCITUN_PACKET) + PacketSize);
        }
    }

    KIRQL Irql = ExAcquireSpinLockShared(&Ctx->TransitionLock);
    NDIS_STATUS Status;
    if ((Status = NDIS_STATUS_PAUSED, !ReadAcquire(&Ctx->Running)) ||
        (Status = NDIS_STATUS_MEDIA_DISCONNECTED, KeReadStateEvent(&Ctx->Device.Disconnected)))
        goto skipNbl;

    VELOCITUN_RING *Ring = Ctx->Device.Send.Ring;
    ULONG RingCapacity = Ctx->Device.Send.Capacity;

    /* Allocate space for packets in the ring. */
    ULONG RingHead = ReadULongAcquire(&Ring->Head);
    if (Status = NDIS_STATUS_ADAPTER_NOT_READY, RingHead >= RingCapacity)
        goto skipNbl;

    KLOCK_QUEUE_HANDLE LockHandle;
    KeAcquireInStackQueuedSpinLock(&Ctx->Device.Send.Lock, &LockHandle);

    ULONG RingTail = Ctx->Device.Send.RingTail;
    ASSERT(RingTail < RingCapacity);

    ULONG RingSpace = VELOCITUN_RING_WRAP(RingHead - RingTail - VELOCITUN_ALIGNMENT, RingCapacity);
    if (Status = NDIS_STATUS_BUFFER_OVERFLOW, RingSpace < RequiredRingSpace)
        goto cleanupKeReleaseInStackQueuedSpinLock;

    Ctx->Device.Send.RingTail = VELOCITUN_RING_WRAP(RingTail + RequiredRingSpace, RingCapacity);
    VelociTunNblSetOffsetAndMarkActive(NetBufferLists, Ctx->Device.Send.RingTail);
    *(Ctx->Device.Send.ActiveNbls.Head ? &NET_BUFFER_LIST_NEXT_NBL_EX(Ctx->Device.Send.ActiveNbls.Tail)
                                       : &Ctx->Device.Send.ActiveNbls.Head) = NetBufferLists;
    Ctx->Device.Send.ActiveNbls.Tail = NetBufferLists;

    KeReleaseInStackQueuedSpinLock(&LockHandle);

    /* Copy packets. */
    for (NET_BUFFER_LIST *Nbl = NetBufferLists; Nbl; Nbl = NET_BUFFER_LIST_NEXT_NBL(Nbl))
    {
        for (NET_BUFFER *Nb = NET_BUFFER_LIST_FIRST_NB(Nbl); Nb; Nb = NET_BUFFER_NEXT_NB(Nb))
        {
            UINT PacketSize = NET_BUFFER_DATA_LENGTH(Nb);
            if (Status = NDIS_STATUS_INVALID_LENGTH, PacketSize > VELOCITUN_MAX_IP_PACKET_SIZE)
                goto skipPacket;

            VELOCITUN_PACKET *Packet = (VELOCITUN_PACKET *)(Ring->Data + RingTail);
            Packet->Size = PacketSize;
            void *NbData = NdisGetDataBuffer(Nb, PacketSize, Packet->Data, 1, 0);
            if (!NbData)
            {
                /* The space for the packet has already been allocated in the ring. Write a zero-packet rather than
                 * fixing the gap in the ring. */
                NdisZeroMemory(Packet->Data, PacketSize);
                DiscardedPacketsCount++;
                NET_BUFFER_LIST_STATUS(Nbl) = NDIS_STATUS_FAILURE;
            }
            else
            {
                if (NbData != Packet->Data)
                    NdisMoveMemory(Packet->Data, NbData, PacketSize);
                SentPacketsCount++;
                SentPacketsSize += PacketSize;
            }

            RingTail = VELOCITUN_RING_WRAP(RingTail + VELOCITUN_ALIGN(sizeof(VELOCITUN_PACKET) + PacketSize), RingCapacity);
            continue;

        skipPacket:
            ErrorPacketsCount++;
            NET_BUFFER_LIST_STATUS(Nbl) = Status;
        }
    }
    ASSERT(RingTail == VelociTunNblGetOffset(NetBufferLists));
    VelociTunNblMarkCompleted(NetBufferLists);

    /* Adjust the ring tail. */
    KeAcquireInStackQueuedSpinLock(&Ctx->Device.Send.Lock, &LockHandle);
    while (Ctx->Device.Send.ActiveNbls.Head && VelociTunNblIsCompleted(Ctx->Device.Send.ActiveNbls.Head))
    {
        NET_BUFFER_LIST *CompletedNbl = Ctx->Device.Send.ActiveNbls.Head;
        Ctx->Device.Send.ActiveNbls.Head = NET_BUFFER_LIST_NEXT_NBL_EX(CompletedNbl);
        WriteULongRelease(&Ring->Tail, VelociTunNblGetOffset(CompletedNbl));
        KeSetEvent(Ctx->Device.Send.TailMoved, IO_NETWORK_INCREMENT, FALSE);
        NdisMSendNetBufferListsComplete(
            Ctx->MiniportAdapterHandle, CompletedNbl, NDIS_SEND_COMPLETE_FLAGS_DISPATCH_LEVEL);
    }
    KeReleaseInStackQueuedSpinLock(&LockHandle);
    ExReleaseSpinLockShared(&Ctx->TransitionLock, Irql);
    goto updateStatistics;

cleanupKeReleaseInStackQueuedSpinLock:
    KeReleaseInStackQueuedSpinLock(&LockHandle);
skipNbl:
    for (NET_BUFFER_LIST *Nbl = NetBufferLists; Nbl; Nbl = NET_BUFFER_LIST_NEXT_NBL(Nbl))
        NET_BUFFER_LIST_STATUS(Nbl) = Status;
    DiscardedPacketsCount += PacketsCount;
    ExReleaseSpinLockShared(&Ctx->TransitionLock, Irql);
    NdisMSendNetBufferListsComplete(Ctx->MiniportAdapterHandle, NetBufferLists, 0);
updateStatistics:
    InterlockedAddNoFence64((LONG64 *)&Ctx->Statistics.ifHCOutOctets, SentPacketsSize);
    InterlockedAddNoFence64((LONG64 *)&Ctx->Statistics.ifHCOutUcastOctets, SentPacketsSize);
    InterlockedAddNoFence64((LONG64 *)&Ctx->Statistics.ifHCOutUcastPkts, SentPacketsCount);
    InterlockedAddNoFence64((LONG64 *)&Ctx->Statistics.ifOutErrors, ErrorPacketsCount);
    InterlockedAddNoFence64((LONG64 *)&Ctx->Statistics.ifOutDiscards, DiscardedPacketsCount);
}

MINIPORT_CANCEL_SEND VelociTunCancelSend;
_Use_decl_annotations_ VOID
VelociTunCancelSend(NDIS_HANDLE MiniportAdapterContext, PVOID CancelId)
{
}

MINIPORT_RETURN_NET_BUFFER_LISTS VelociTunReturnNetBufferLists;
_Use_decl_annotations_ VOID
VelociTunReturnNetBufferLists(NDIS_HANDLE MiniportAdapterContext, PNET_BUFFER_LIST NetBufferLists, ULONG ReturnFlags)
{
    VELOCITUN_CTX *Ctx = (VELOCITUN_CTX *)MiniportAdapterContext;
    VELOCITUN_RING *Ring = Ctx->Device.Receive.Ring;

    LONG64 ReceivedPacketsCount = 0, ReceivedPacketsSize = 0, ErrorPacketsCount = 0;
    for (NET_BUFFER_LIST *Nbl = NetBufferLists, *NextNbl; Nbl; Nbl = NextNbl)
    {
        NextNbl = NET_BUFFER_LIST_NEXT_NBL(Nbl);

        if (NT_SUCCESS(NET_BUFFER_LIST_STATUS(Nbl)))
        {
            ReceivedPacketsCount++;
            ReceivedPacketsSize += NET_BUFFER_LIST_FIRST_NB(Nbl)->DataLength;
        }
        else
            ErrorPacketsCount++;

        VelociTunNblMarkCompleted(Nbl);
        for (;;)
        {
            KLOCK_QUEUE_HANDLE LockHandle;
            KeAcquireInStackQueuedSpinLock(&Ctx->Device.Receive.Lock, &LockHandle);
            NET_BUFFER_LIST *CompletedNbl = Ctx->Device.Receive.ActiveNbls.Head;
            if (!CompletedNbl || !VelociTunNblIsCompleted(CompletedNbl))
            {
                KeReleaseInStackQueuedSpinLock(&LockHandle);
                break;
            }
            Ctx->Device.Receive.ActiveNbls.Head = NET_BUFFER_LIST_NEXT_NBL_EX(CompletedNbl);
            if (!Ctx->Device.Receive.ActiveNbls.Head)
                KeSetEvent(&Ctx->Device.Receive.ActiveNbls.Empty, IO_NO_INCREMENT, FALSE);
            KeReleaseInStackQueuedSpinLock(&LockHandle);
            WriteULongRelease(&Ring->Head, VelociTunNblGetOffset(CompletedNbl));
            const MDL *TargetMdl = Ctx->Device.Receive.Mdl;
            for (MDL *Mdl = NET_BUFFER_FIRST_MDL(NET_BUFFER_LIST_FIRST_NB(CompletedNbl)); Mdl; Mdl = Mdl->Next)
            {
                if (MmGetMdlVirtualAddress(Mdl) < MmGetMdlVirtualAddress(TargetMdl) ||
                    (UCHAR *)MmGetMdlVirtualAddress(Mdl) + MmGetMdlByteCount(Mdl) >
                        (UCHAR *)MmGetMdlVirtualAddress(TargetMdl) + MmGetMdlByteCount(TargetMdl))
                    continue;
                IoFreeMdl(Mdl);
                break;
            }
            NdisFreeNetBufferList(CompletedNbl);
        }
    }

    InterlockedAddNoFence64((LONG64 *)&Ctx->Statistics.ifHCInOctets, ReceivedPacketsSize);
    InterlockedAddNoFence64((LONG64 *)&Ctx->Statistics.ifHCInUcastOctets, ReceivedPacketsSize);
    InterlockedAddNoFence64((LONG64 *)&Ctx->Statistics.ifHCInUcastPkts, ReceivedPacketsCount);
    InterlockedAddNoFence64((LONG64 *)&Ctx->Statistics.ifInErrors, ErrorPacketsCount);
}

_IRQL_requires_max_(PASSIVE_LEVEL)
    _Function_class_(KSTART_ROUTINE) static VOID
    VelociTunProcessReceiveData(_Inout_ VELOCITUN_CTX *Ctx)
{
    KeSetPriorityThread(KeGetCurrentThread(), 1);

    VELOCITUN_RING *Ring = Ctx->Device.Receive.Ring;
    ULONG RingCapacity = Ctx->Device.Receive.Capacity;
    LARGE_INTEGER Frequency;
    KeQueryPerformanceCounter(&Frequency);
    ULONG64 SpinMax = Frequency.QuadPart / 1000 / 10; /* 1/10 ms */
    VOID *Events[] = {&Ctx->Device.Disconnected, Ctx->Device.Receive.TailMoved};
    ASSERT(RTL_NUMBER_OF(Events) <= THREAD_WAIT_OBJECTS);

    ULONG RingHead = ReadULongAcquire(&Ring->Head);
    if (RingHead >= RingCapacity)
        goto cleanup;

    while (!KeReadStateEvent(&Ctx->Device.Disconnected))
    {
        /* Get next packet from the ring. */
        ULONG RingTail = ReadULongAcquire(&Ring->Tail);
        if (RingHead == RingTail)
        {
            LARGE_INTEGER SpinStart = KeQueryPerformanceCounter(NULL);
            for (;;)
            {
                RingTail = ReadULongAcquire(&Ring->Tail);
                if (RingTail != RingHead)
                    break;
                if (KeReadStateEvent(&Ctx->Device.Disconnected))
                    break;
                LARGE_INTEGER SpinNow = KeQueryPerformanceCounter(NULL);
                if ((ULONG64)SpinNow.QuadPart - (ULONG64)SpinStart.QuadPart >= SpinMax)
                    break;
                ZwYieldExecution();
            }
            if (RingHead == RingTail)
            {
                WriteRelease(&Ring->Alertable, TRUE);
                RingTail = ReadULongAcquire(&Ring->Tail);
                if (RingHead == RingTail)
                {
                    KeWaitForMultipleObjects(
                        RTL_NUMBER_OF(Events), Events, WaitAny, Executive, KernelMode, FALSE, NULL, NULL);
                    WriteRelease(&Ring->Alertable, FALSE);
                    continue;
                }
                WriteRelease(&Ring->Alertable, FALSE);
                KeClearEvent(Ctx->Device.Receive.TailMoved);
            }
        }
        if (RingTail >= RingCapacity)
            break;

        ULONG RingContent = VELOCITUN_RING_WRAP(RingTail - RingHead, RingCapacity);
        if (RingContent < sizeof(VELOCITUN_PACKET))
            break;

        VELOCITUN_PACKET *Packet = (VELOCITUN_PACKET *)(Ring->Data + RingHead);
        ULONG PacketSize = *(volatile ULONG *)&Packet->Size;
        if (PacketSize > VELOCITUN_MAX_IP_PACKET_SIZE)
            break;

        ULONG AlignedPacketSize = VELOCITUN_ALIGN(sizeof(VELOCITUN_PACKET) + PacketSize);
        if (AlignedPacketSize > RingContent)
            break;

        RingHead = VELOCITUN_RING_WRAP(RingHead + AlignedPacketSize, RingCapacity);

        ULONG NblFlags;
        USHORT NblProto;
        if (PacketSize >= 20 && Packet->Data[0] >> 4 == 4)
        {
            NblFlags = NDIS_NBL_FLAGS_IS_IPV4;
            NblProto = HTONS(NDIS_ETH_TYPE_IPV4);
        }
        else if (PacketSize >= 40 && Packet->Data[0] >> 4 == 6)
        {
            NblFlags = NDIS_NBL_FLAGS_IS_IPV6;
            NblProto = HTONS(NDIS_ETH_TYPE_IPV6);
        }
        else
            goto skipNbl;

        VOID *PacketAddr =
            (UCHAR *)MmGetMdlVirtualAddress(Ctx->Device.Receive.Mdl) + (ULONG)(Packet->Data - (UCHAR *)Ring);
        MDL *Mdl = IoAllocateMdl(PacketAddr, PacketSize, FALSE, FALSE, NULL);
        if (!Mdl)
            goto skipNbl;
        IoBuildPartialMdl(Ctx->Device.Receive.Mdl, Mdl, PacketAddr, PacketSize);
        NET_BUFFER_LIST *Nbl = NdisAllocateNetBufferAndNetBufferList(Ctx->NblPool, 0, 0, Mdl, 0, PacketSize);
        if (!Nbl)
            goto cleanupMdl;
        Nbl->SourceHandle = Ctx->MiniportAdapterHandle;
        NdisSetNblFlag(Nbl, NblFlags);
        NET_BUFFER_LIST_INFO(Nbl, NetBufferListFrameType) = (PVOID)NblProto;
        NET_BUFFER_LIST_STATUS(Nbl) = NDIS_STATUS_SUCCESS;
        VelociTunNblSetOffsetAndMarkActive(Nbl, RingHead);

        KIRQL Irql = ExAcquireSpinLockShared(&Ctx->TransitionLock);
        if (!ReadAcquire(&Ctx->Running))
            goto cleanupNbl;

        KLOCK_QUEUE_HANDLE LockHandle;
        KeAcquireInStackQueuedSpinLock(&Ctx->Device.Receive.Lock, &LockHandle);
        if (Ctx->Device.Receive.ActiveNbls.Head)
            NET_BUFFER_LIST_NEXT_NBL_EX(Ctx->Device.Receive.ActiveNbls.Tail) = Nbl;
        else
        {
            KeClearEvent(&Ctx->Device.Receive.ActiveNbls.Empty);
            Ctx->Device.Receive.ActiveNbls.Head = Nbl;
        }
        Ctx->Device.Receive.ActiveNbls.Tail = Nbl;
        KeReleaseInStackQueuedSpinLock(&LockHandle);

        NdisMIndicateReceiveNetBufferLists(
            Ctx->MiniportAdapterHandle,
            Nbl,
            NDIS_DEFAULT_PORT_NUMBER,
            1,
            NDIS_RECEIVE_FLAGS_DISPATCH_LEVEL | NDIS_RECEIVE_FLAGS_SINGLE_ETHER_TYPE);

        ExReleaseSpinLockShared(&Ctx->TransitionLock, Irql);
        continue;

    cleanupNbl:
        ExReleaseSpinLockShared(&Ctx->TransitionLock, Irql);
        NdisFreeNetBufferList(Nbl);
    cleanupMdl:
        IoFreeMdl(Mdl);
    skipNbl:
        InterlockedIncrementNoFence64((LONG64 *)&Ctx->Statistics.ifInDiscards);
        KeWaitForSingleObject(&Ctx->Device.Receive.ActiveNbls.Empty, Executive, KernelMode, FALSE, NULL);
        WriteULongRelease(&Ring->Head, RingHead);
    }

    /* Wait for all NBLs to return: 1. To prevent race between proceeding and invalidating ring head. 2. To have
     * VelociTunDispatchUnregisterBuffers() implicitly wait before releasing ring MDL used by NBL(s). */
    KeWaitForSingleObject(&Ctx->Device.Receive.ActiveNbls.Empty, Executive, KernelMode, FALSE, NULL);
cleanup:
    WriteULongRelease(&Ring->Head, MAXULONG);
}

#define IS_POW2(x) ((x) && !((x) & ((x) - 1)))

_IRQL_requires_max_(PASSIVE_LEVEL)
    _Must_inspect_result_
    static NTSTATUS
    VelociTunRegisterBuffers(_Inout_ VELOCITUN_CTX *Ctx, _Inout_ IRP *Irp)
{
    NTSTATUS Status = STATUS_ALREADY_INITIALIZED;
    IO_STACK_LOCATION *Stack = IoGetCurrentIrpStackLocation(Irp);

    if (!ExAcquireResourceExclusiveLite(&Ctx->Device.RegistrationLock, FALSE))
        return Status;

    if (Ctx->Device.OwningFileObject)
        goto cleanupMutex;
    Ctx->Device.OwningFileObject = Stack->FileObject;

    VELOCITUN_REGISTER_RINGS Rrb;
    if (Stack->Parameters.DeviceIoControl.InputBufferLength == sizeof(Rrb))
        NdisMoveMemory(&Rrb, Irp->AssociatedIrp.SystemBuffer, sizeof(Rrb));
#ifdef _WIN64
    else if (
        IoIs32bitProcess(Irp) && Stack->Parameters.DeviceIoControl.InputBufferLength == sizeof(VELOCITUN_REGISTER_RINGS_32))
    {
        VELOCITUN_REGISTER_RINGS_32 *Rrb32 = Irp->AssociatedIrp.SystemBuffer;
        Rrb.Send.RingSize = Rrb32->Send.RingSize;
        Rrb.Send.Ring = (VELOCITUN_RING *)Rrb32->Send.Ring;
        Rrb.Send.TailMoved = (HANDLE)Rrb32->Send.TailMoved;
        Rrb.Receive.RingSize = Rrb32->Receive.RingSize;
        Rrb.Receive.Ring = (VELOCITUN_RING *)Rrb32->Receive.Ring;
        Rrb.Receive.TailMoved = (HANDLE)Rrb32->Receive.TailMoved;
    }
#endif
    else
    {
        Status = STATUS_INVALID_PARAMETER;
        goto cleanupResetOwner;
    }

    Ctx->Device.Send.Capacity = VELOCITUN_RING_CAPACITY(Rrb.Send.RingSize);
    if (Status = STATUS_INVALID_PARAMETER,
        (Ctx->Device.Send.Capacity < VELOCITUN_MIN_RING_CAPACITY || Ctx->Device.Send.Capacity > VELOCITUN_MAX_RING_CAPACITY ||
         !IS_POW2(Ctx->Device.Send.Capacity) || !Rrb.Send.TailMoved || !Rrb.Send.Ring))
        goto cleanupResetOwner;

    if (!NT_SUCCESS(
            Status = ObReferenceObjectByHandle(
                Rrb.Send.TailMoved,
                /* We will not wait on send ring tail moved event. */
                EVENT_MODIFY_STATE,
                *ExEventObjectType,
                Irp->RequestorMode,
                &Ctx->Device.Send.TailMoved,
                NULL)))
        goto cleanupResetOwner;

    Ctx->Device.Send.Mdl = IoAllocateMdl(Rrb.Send.Ring, Rrb.Send.RingSize, FALSE, FALSE, NULL);
    if (Status = STATUS_INSUFFICIENT_RESOURCES, !Ctx->Device.Send.Mdl)
        goto cleanupSendTailMoved;
    try
    {
        Status = STATUS_INVALID_USER_BUFFER;
        MmProbeAndLockPages(Ctx->Device.Send.Mdl, Irp->RequestorMode, IoWriteAccess);
    }
    except(EXCEPTION_EXECUTE_HANDLER) { goto cleanupSendMdl; }

    Ctx->Device.Send.Ring =
        MmGetSystemAddressForMdlSafe(Ctx->Device.Send.Mdl, NormalPagePriority | MdlMappingNoExecute);
    if (Status = STATUS_INSUFFICIENT_RESOURCES, !Ctx->Device.Send.Ring)
        goto cleanupSendUnlockPages;

    Ctx->Device.Send.RingTail = ReadULongAcquire(&Ctx->Device.Send.Ring->Tail);
    if (Status = STATUS_INVALID_PARAMETER, Ctx->Device.Send.RingTail >= Ctx->Device.Send.Capacity)
        goto cleanupSendUnlockPages;

    Ctx->Device.Receive.Capacity = VELOCITUN_RING_CAPACITY(Rrb.Receive.RingSize);
    if (Status = STATUS_INVALID_PARAMETER,
        (Ctx->Device.Receive.Capacity < VELOCITUN_MIN_RING_CAPACITY || Ctx->Device.Receive.Capacity > VELOCITUN_MAX_RING_CAPACITY ||
         !IS_POW2(Ctx->Device.Receive.Capacity) || !Rrb.Receive.TailMoved || !Rrb.Receive.Ring))
        goto cleanupSendUnlockPages;

    if (!NT_SUCCESS(
            Status = ObReferenceObjectByHandle(
                Rrb.Receive.TailMoved,
                /* We need to clear receive ring TailMoved event on transition to non-alertable state. */
                SYNCHRONIZE | EVENT_MODIFY_STATE,
                *ExEventObjectType,
                Irp->RequestorMode,
                &Ctx->Device.Receive.TailMoved,
                NULL)))
        goto cleanupSendUnlockPages;

    Ctx->Device.Receive.Mdl = IoAllocateMdl(Rrb.Receive.Ring, Rrb.Receive.RingSize, FALSE, FALSE, NULL);
    if (Status = STATUS_INSUFFICIENT_RESOURCES, !Ctx->Device.Receive.Mdl)
        goto cleanupReceiveTailMoved;
    try
    {
        Status = STATUS_INVALID_USER_BUFFER;
        MmProbeAndLockPages(Ctx->Device.Receive.Mdl, Irp->RequestorMode, IoWriteAccess);
    }
    except(EXCEPTION_EXECUTE_HANDLER) { goto cleanupReceiveMdl; }

    Ctx->Device.Receive.Ring =
        MmGetSystemAddressForMdlSafe(Ctx->Device.Receive.Mdl, NormalPagePriority | MdlMappingNoExecute);
    if (Status = STATUS_INSUFFICIENT_RESOURCES, !Ctx->Device.Receive.Ring)
        goto cleanupReceiveUnlockPages;

    KeClearEvent(&Ctx->Device.Disconnected);

    OBJECT_ATTRIBUTES ObjectAttributes;
    InitializeObjectAttributes(&ObjectAttributes, NULL, OBJ_KERNEL_HANDLE, NULL, NULL);
    if (Status = NDIS_STATUS_FAILURE,
        !NT_SUCCESS(PsCreateSystemThread(
            &Ctx->Device.Receive.Thread, THREAD_ALL_ACCESS, &ObjectAttributes, NULL, NULL, VelociTunProcessReceiveData, Ctx)))
        goto cleanupFlagsConnected;

    Ctx->Device.OwningProcessId = PsGetCurrentProcessId();
    InitializeListHead(&Ctx->Device.Entry);
    ExAcquireResourceExclusiveLite(&VelociTunDispatchDeviceListLock, TRUE);
    InsertTailList(&VelociTunDispatchDeviceList, &Ctx->Device.Entry);
    ExReleaseResourceLite(&VelociTunDispatchDeviceListLock);

    ExReleaseResourceLite(&Ctx->Device.RegistrationLock);
    VelociTunIndicateStatus(Ctx->MiniportAdapterHandle, MediaConnectStateConnected);
    return STATUS_SUCCESS;

cleanupFlagsConnected:
    KeSetEvent(&Ctx->Device.Disconnected, IO_NO_INCREMENT, FALSE);
    ExReleaseSpinLockExclusive(
        &Ctx->TransitionLock,
        ExAcquireSpinLockExclusive(&Ctx->TransitionLock)); /* Ensure above change is visible to all readers. */
cleanupReceiveUnlockPages:
    MmUnlockPages(Ctx->Device.Receive.Mdl);
cleanupReceiveMdl:
    IoFreeMdl(Ctx->Device.Receive.Mdl);
cleanupReceiveTailMoved:
    ObDereferenceObject(Ctx->Device.Receive.TailMoved);
cleanupSendUnlockPages:
    MmUnlockPages(Ctx->Device.Send.Mdl);
cleanupSendMdl:
    IoFreeMdl(Ctx->Device.Send.Mdl);
cleanupSendTailMoved:
    ObDereferenceObject(Ctx->Device.Send.TailMoved);
cleanupResetOwner:
    Ctx->Device.OwningFileObject = NULL;
cleanupMutex:
    ExReleaseResourceLite(&Ctx->Device.RegistrationLock);
    return Status;
}

#define VELOCITUN_FORCE_UNREGISTRATION ((FILE_OBJECT *)-1)
_IRQL_requires_max_(PASSIVE_LEVEL) VOID
    VelociTunUnregisterBuffers(_Inout_ VELOCITUN_CTX *Ctx, _In_ FILE_OBJECT *Owner)
{
    if (!Owner)
        return;
    ExAcquireResourceExclusiveLite(&Ctx->Device.RegistrationLock, TRUE);
    if (!Ctx->Device.OwningFileObject || (Owner != VELOCITUN_FORCE_UNREGISTRATION && Ctx->Device.OwningFileObject != Owner))
    {
        ExReleaseResourceLite(&Ctx->Device.RegistrationLock);
        return;
    }
    Ctx->Device.OwningFileObject = NULL;

    ExAcquireResourceExclusiveLite(&VelociTunDispatchDeviceListLock, TRUE);
    RemoveEntryList(&Ctx->Device.Entry);
    ExReleaseResourceLite(&VelociTunDispatchDeviceListLock);

    VelociTunIndicateStatus(Ctx->MiniportAdapterHandle, MediaConnectStateDisconnected);

    KeSetEvent(&Ctx->Device.Disconnected, IO_NO_INCREMENT, FALSE);
    ExReleaseSpinLockExclusive(
        &Ctx->TransitionLock,
        ExAcquireSpinLockExclusive(&Ctx->TransitionLock)); /* Ensure above change is visible to all readers. */

    PKTHREAD ThreadObject;
    if (NT_SUCCESS(
            ObReferenceObjectByHandle(Ctx->Device.Receive.Thread, SYNCHRONIZE, NULL, KernelMode, &ThreadObject, NULL)))
    {
        KeWaitForSingleObject(ThreadObject, Executive, KernelMode, FALSE, NULL);
        ObDereferenceObject(ThreadObject);
    }
    ZwClose(Ctx->Device.Receive.Thread);

    WriteULongRelease(&Ctx->Device.Send.Ring->Tail, MAXULONG);
    KeSetEvent(Ctx->Device.Send.TailMoved, IO_NO_INCREMENT, FALSE);

    MmUnlockPages(Ctx->Device.Receive.Mdl);
    IoFreeMdl(Ctx->Device.Receive.Mdl);
    ObDereferenceObject(Ctx->Device.Receive.TailMoved);
    MmUnlockPages(Ctx->Device.Send.Mdl);
    IoFreeMdl(Ctx->Device.Send.Mdl);
    ObDereferenceObject(Ctx->Device.Send.TailMoved);

    ExReleaseResourceLite(&Ctx->Device.RegistrationLock);
}

_IRQL_requires_max_(PASSIVE_LEVEL) VOID
    VelociTunProcessNotification(HANDLE ParentId, HANDLE ProcessId, BOOLEAN Create)
{
    if (Create)
        return;
    ExAcquireSharedStarveExclusive(&VelociTunDispatchDeviceListLock, TRUE);
    VELOCITUN_CTX *Ctx = NULL;
    for (LIST_ENTRY *Entry = VelociTunDispatchDeviceList.Flink; Entry != &VelociTunDispatchDeviceList; Entry = Entry->Flink)
    {
        VELOCITUN_CTX *Candidate = CONTAINING_RECORD(Entry, VELOCITUN_CTX, Device.Entry);
        if (Candidate->Device.OwningProcessId == ProcessId)
        {
            Ctx = Candidate;
            break;
        }
    }
    ExReleaseResourceLite(&VelociTunDispatchDeviceListLock);
    if (!Ctx)
        return;

    VelociTunUnregisterBuffers(Ctx, VELOCITUN_FORCE_UNREGISTRATION);
}

_Dispatch_type_(IRP_MJ_DEVICE_CONTROL) DRIVER_DISPATCH_PAGED VelociTunDispatchDeviceControl;
_Use_decl_annotations_ NTSTATUS
VelociTunDispatchDeviceControl(DEVICE_OBJECT *DeviceObject, IRP *Irp)
{
    IO_STACK_LOCATION *Stack = IoGetCurrentIrpStackLocation(Irp);
    if (Stack->Parameters.DeviceIoControl.IoControlCode != VELOCITUN_IOCTL_REGISTER_RINGS)
        return NdisDispatchDeviceControl(DeviceObject, Irp);

    SECURITY_SUBJECT_CONTEXT SubjectContext;
    SeCaptureSubjectContext(&SubjectContext);
    NTSTATUS Status;
    ACCESS_MASK GrantedAccess;
    BOOLEAN HasAccess = SeAccessCheck(
        VelociTunDispatchSecurityDescriptor,
        &SubjectContext,
        FALSE,
        FILE_WRITE_DATA,
        0,
        NULL,
        IoGetFileObjectGenericMapping(),
        Irp->RequestorMode,
        &GrantedAccess,
        &Status);
    SeReleaseSubjectContext(&SubjectContext);
    if (!HasAccess)
        goto cleanup;
    switch (Stack->Parameters.DeviceIoControl.IoControlCode)
    {
    case VELOCITUN_IOCTL_REGISTER_RINGS:
    {
        KeEnterCriticalRegion();
        ExAcquireResourceSharedLite(&VelociTunDispatchCtxGuard, TRUE);
#pragma warning(suppress : 28175)
        VELOCITUN_CTX *Ctx = DeviceObject->Reserved;
        Status = NDIS_STATUS_ADAPTER_NOT_READY;
        if (Ctx)
            Status = VelociTunRegisterBuffers(Ctx, Irp);
        ExReleaseResourceLite(&VelociTunDispatchCtxGuard);
        KeLeaveCriticalRegion();
        break;
    }
    }
cleanup:
    Irp->IoStatus.Status = Status;
    Irp->IoStatus.Information = 0;
    IoCompleteRequest(Irp, IO_NO_INCREMENT);
    return Status;
}

_Dispatch_type_(IRP_MJ_CLOSE) DRIVER_DISPATCH_PAGED VelociTunDispatchClose;
_Use_decl_annotations_ NTSTATUS
VelociTunDispatchClose(DEVICE_OBJECT *DeviceObject, IRP *Irp)
{
    KeEnterCriticalRegion();
    ExAcquireResourceSharedLite(&VelociTunDispatchCtxGuard, TRUE);
#pragma warning(suppress : 28175)
    VELOCITUN_CTX *Ctx = DeviceObject->Reserved;
    if (Ctx)
        VelociTunUnregisterBuffers(Ctx, IoGetCurrentIrpStackLocation(Irp)->FileObject);
    ExReleaseResourceLite(&VelociTunDispatchCtxGuard);
    KeLeaveCriticalRegion();
    return NdisDispatchClose(DeviceObject, Irp);
}

// External declarations for functions defined in velocitun_ndis.c
extern UINT NdisVersion;
extern NDIS_HANDLE NdisMiniportDriverHandle;
extern DRIVER_DISPATCH *NdisDispatchDeviceControl, *NdisDispatchClose, *NdisDispatchPnp;

// Function declarations
VOID VelociTunUnregisterBuffers(_Inout_ VELOCITUN_CTX *Ctx, _In_ FILE_OBJECT *Owner);
NTSTATUS VelociTunRegisterBuffers(_Inout_ VELOCITUN_CTX *Ctx, _Inout_ IRP *Irp);
VOID VelociTunProcessNotification(HANDLE ParentId, HANDLE ProcessId, BOOLEAN Create);

#define VELOCITUN_FORCE_UNREGISTRATION ((FILE_OBJECT *)-1)
