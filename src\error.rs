use thiserror::Error;
use windows::Win32::Foundation::WIN32_ERROR;

#[derive(Error, Debug)]
pub enum VelociTunError {
    #[error("Windows API error: {0}")]
    WindowsApi(#[from] windows::core::Error),

    #[error("Invalid parameter: {0}")]
    InvalidParameter(String),

    #[error("Buffer overflow")]
    BufferOverflow,

    #[error("No more items")]
    NoMoreItems,

    #[error("Handle EOF")]
    HandleEof,

    #[error("Invalid data")]
    InvalidData,

    #[error("Adapter not found")]
    AdapterNotFound,

    #[error("Driver not loaded")]
    DriverNotLoaded,

    #[error("Session already active")]
    SessionActive,

    #[error("Ring capacity invalid: {0}")]
    InvalidRingCapacity(u32),

    #[error("Operation timed out")]
    Timeout,
}

impl From<VelociTunError> for WIN32_ERROR {
    fn from(err: VelociTunError) -> Self {
        match err {
            VelociTunError::WindowsApi(e) => WIN32_ERROR(e.code().0 as u32),
            VelociTunError::InvalidParameter(_) => WIN32_ERROR(87), // ERROR_INVALID_PARAMETER
            VelociTunError::BufferOverflow => WIN32_ERROR(111),     // ERROR_BUFFER_OVERFLOW
            VelociTunError::NoMoreItems => WIN32_ERROR(259),        // ERROR_NO_MORE_ITEMS
            VelociTunError::HandleEof => WIN32_ERROR(38),           // ERROR_HANDLE_EOF
            VelociTunError::InvalidData => WIN32_ERROR(13),         // ERROR_INVALID_DATA
            VelociTunError::AdapterNotFound => WIN32_ERROR(2),      // ERROR_FILE_NOT_FOUND
            VelociTunError::DriverNotLoaded => WIN32_ERROR(2),      // ERROR_FILE_NOT_FOUND
            VelociTunError::SessionActive => WIN32_ERROR(1816),     // ERROR_DEVICE_IN_USE
            VelociTunError::InvalidRingCapacity(_) => WIN32_ERROR(87), // ERROR_INVALID_PARAMETER
            VelociTunError::Timeout => WIN32_ERROR(258),            // ERROR_WAIT_TIMEOUT
        }
    }
}

pub type VelociTunResult<T> = Result<T, VelociTunError>;
