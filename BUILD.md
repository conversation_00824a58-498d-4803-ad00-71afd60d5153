# VelociTun 构建指南

本文档说明如何在 Rust 项目中编译 VelociTun 驱动程序。

## 前置要求

### 必需工具

- **Visual Studio 2019/2022** (包含 C++ 工作负载)
- **Windows Driver Kit (WDK) 10**
- **Rust 1.70+** (用于用户空间库)

### 可选工具

- **cargo-make** (用于高级构建任务): `cargo install cargo-make`

## 构建方法

### 1. 自动构建 (推荐)

当您运行 `cargo build` 时，构建脚本会自动尝试编译驱动：

```bash
# 构建 Rust 项目，同时自动编译驱动
cargo build

# 构建发布版本
cargo build --release
```

构建脚本会：

- 自动查找 MSBuild
- 编译内核驱动 (用户空间功能由 Rust 实现)
- 将驱动文件复制到 `resources/` 目录
- 嵌入到 Rust 二进制文件中

### 2. 使用 PowerShell 脚本

```powershell
# 显示帮助
.\build-driver.ps1 -Help

# 构建发布版本 (默认 x64)
.\build-driver.ps1

# 构建调试版本
.\build-driver.ps1 -Debug

# 清理并重新构建
.\build-driver.ps1 -Clean

# 构建 ARM64 版本
.\build-driver.ps1 -Platform ARM64
```

### 3. 使用 cargo-make

```bash
# 安装 cargo-make (一次性)
cargo install cargo-make

# 构建驱动
cargo make build-driver

# 清理驱动构建产物
cargo make clean-driver

# 构建所有 (Rust + 驱动)
cargo make build-all

# 测试驱动文件是否存在
cargo make test-driver
```

### 4. 手动构建

```powershell
# 使用 MSBuild 直接构建
& "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" `
  driver\velocitun.vcxproj `
  /p:Configuration=Release `
  /p:Platform=x64 `
  /p:EnableInfVerif=false `
  /p:SignMode=Off
```

## 构建输出

### 驱动文件位置

构建成功后，驱动文件会生成在以下位置：

```
driver/x64/Release/
├── velocitun.sys           # 驱动文件
├── velocitun.pdb           # 调试符号
└── velocitun/              # 驱动包
    ├── velocitun.sys       # 驱动文件
    ├── velocitun.inf       # 安装信息文件
    └── velocitun.cat       # 目录文件
```

### 资源文件

构建脚本会自动将驱动文件复制到 `resources/` 目录：

```
resources/
├── velocitun-amd64.sys     # x64 驱动文件
├── velocitun-amd64.inf     # 安装信息文件
└── velocitun-amd64.cat     # 目录文件
```

这些文件会被嵌入到 Rust 二进制文件中，用于运行时部署。
用户空间功能完全由 Rust 代码实现，无需额外的 DLL。

## Git 忽略配置

项目已配置 `.gitignore` 来忽略构建产物：

```gitignore
# 驱动构建输出
/driver/x64/
/driver/Win32/
/driver/ARM64/
/driver/Debug/
/driver/Release/

# 驱动中间文件
/driver/*-intermediate/
/driver/*.log
/driver/*.pdb
/driver/*.obj

# 可选：取消注释以忽略生成的资源文件
# resources/velocitun-amd64.sys
# resources/velocitun.inf
# resources/velocitun.cat
```

## 环境变量

构建脚本支持以下环境变量：

- `VELOCITUN_DRIVER_BUILT=1` - 构建成功时设置
- `WDK_PATH` - 手动指定 WDK 路径 (可选)

## 特性标志

- `arm64-driver` - 启用 ARM64 驱动支持

```bash
# 构建包含 ARM64 驱动的版本
cargo build --features arm64-driver
```

## 故障排除

### MSBuild 未找到

确保安装了 Visual Studio 2019/2022 并包含 C++ 工作负载。

### WDK 相关错误

确保安装了 Windows Driver Kit 10。

### 权限错误

以管理员权限运行命令提示符。

### 签名错误

开发环境中可以使用 `/p:SignMode=Off` 跳过签名。

## 高级配置

### 自定义构建参数

修改 `build.rs` 中的 MSBuild 参数：

```rust
let output = Command::new(&msbuild_path)
    .arg(&driver_project)
    .arg("/p:Configuration=Release")
    .arg("/p:Platform=x64")
    .arg("/p:TargetVersion=Windows10")
    .arg("/p:EnableInfVerif=false")
    .arg("/p:SignMode=Off")
    .output();
```

### 添加新平台支持

1. 在 `build.rs` 中添加平台检测
2. 在 `copy_driver_files_to_resources()` 中添加文件映射
3. 在 `src/resources.rs` 中添加资源嵌入

## 示例工作流

```bash
# 1. 克隆项目
git clone <repository>
cd velocitun

# 2. 构建项目 (自动编译驱动)
cargo build --release

# 3. 运行测试
cargo test

# 4. 检查驱动文件
cargo make test-driver

# 5. 清理构建产物
cargo make clean-driver
```

这样您就可以在 Rust 项目中无缝集成驱动编译了！
