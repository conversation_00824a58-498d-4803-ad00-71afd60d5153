use log::{error, info, warn};
use std::sync::Arc;
use std::thread;
use std::time::{Duration, Instant};
use velocitun::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize env_logger to see log output
    env_logger::init();

    // Initialize the library
    velocitun::init();

    info!("VelociTun Rust Library v{}", velocitun::version());
    info!(
        "Platform: {}",
        if velocitun::is_windows() {
            "Windows"
        } else {
            "Other"
        }
    );

    // Ensure driver is available
    match velocitun::ensure_driver() {
        Ok(()) => info!("Driver is available"),
        Err(e) => warn!("Driver not available: {}", e),
    }

    // List existing adapters
    match velocitun::list_adapters() {
        Ok(adapters) => {
            info!("Found {} existing adapters:", adapters.len());
            for adapter in adapters {
                info!("  - {} ({})", adapter.name(), adapter.tunnel_type());
            }
        }
        Err(e) => warn!("Could not list adapters: {}", e),
    }

    // Create a new adapter
    info!("Creating new adapter...");
    let adapter = match AdapterBuilder::new()
        .name("MyTunnelAdapter")
        .tunnel_type("VelociTun")
        .build()
    {
        Ok(adapter) => {
            info!("Created adapter: {}", adapter.name());
            adapter
        }
        Err(e) => {
            error!("Failed to create adapter: {}", e);
            return Ok(());
        }
    };

    // Test the high-performance Session
    info!("=== Testing High-Performance Session ===");
    test_session(&adapter)?;

    info!("=== Performance Test ===");
    performance_test(&adapter)?;

    info!("All tests completed successfully!");
    Ok(())
}

fn test_session(adapter: &Adapter) -> Result<(), Box<dyn std::error::Error>> {
    // Create Session
    info!("Creating Session...");
    let session = match adapter.start_session(0x400000) {
        Ok(session) => {
            info!("Session created with capacity: {}", session.capacity());
            session
        }
        Err(e) => {
            error!("Failed to create Session: {}", e);
            return Ok(());
        }
    };

    // Create test packet
    let test_data = create_test_packet();

    // Send packet
    info!("Sending test packet...");
    match session.send_packet(&test_data) {
        Ok(()) => info!("Packet sent successfully"),
        Err(e) => warn!("Failed to send packet: {}", e),
    }

    // Try to receive packet (non-blocking)
    info!("Attempting to receive packet...");
    match session.try_receive_packet() {
        Ok(Some(data)) => {
            info!("Received packet: {} bytes", data.len());
            print_packet_info(&data);
        }
        Ok(None) => info!("No packet available"),
        Err(e) => warn!("Error receiving packet: {}", e),
    }

    // Show session statistics
    let stats = session.stats();
    info!("Session Statistics:");
    info!(
        "  Send ring: {}/{} used",
        stats.send_ring_used,
        stats.send_ring_used + stats.send_ring_available
    );
    info!(
        "  Receive ring: {}/{} used",
        stats.receive_ring_used,
        stats.receive_ring_used + stats.receive_ring_available
    );
    info!("  Active: {}", stats.is_active);

    // Demonstrate multi-threading capability
    info!("Demonstrating multi-threaded packet handling...");
    demonstrate_threaded_io(&session);

    Ok(())
}

fn demonstrate_threaded_io(session: &Session) {
    let session = Arc::new(session.clone());

    // Multiple sender threads
    let mut sender_handles = Vec::new();
    for thread_id in 0..3 {
        let sender = session.create_sender();
        let session_clone = session.clone();
        let handle = thread::spawn(move || {
            for i in 0..5 {
                let mut packet = create_test_packet();
                packet[27] = (thread_id * 10 + i) as u8; // Vary sequence number

                match sender.send(&packet) {
                    Ok(()) => info!("  Thread {}: Sent packet {}", thread_id, i + 1),
                    Err(e) => warn!(
                        "  Thread {}: Failed to send packet {}: {}",
                        thread_id,
                        i + 1,
                        e
                    ),
                }

                // Notify receivers
                session_clone.notify_receivers();
                thread::sleep(Duration::from_millis(50));
            }
        });
        sender_handles.push(handle);
    }

    // Multiple receiver threads
    let mut receiver_handles = Vec::new();
    for thread_id in 0..2 {
        let receiver = session.create_receiver();
        let handle = thread::spawn(move || {
            let mut received_count = 0;
            for _ in 0..10 {
                match receiver.receive_timeout(Duration::from_millis(200)) {
                    Ok(data) => {
                        received_count += 1;
                        info!(
                            "  Receiver {}: Got packet {} ({} bytes)",
                            thread_id,
                            received_count,
                            data.len()
                        );
                    }
                    Err(_) => break, // Timeout
                }
            }
            info!(
                "  Receiver {}: Finished with {} packets",
                thread_id, received_count
            );
        });
        receiver_handles.push(handle);
    }

    // Wait for all threads to complete
    for handle in sender_handles {
        let _ = handle.join();
    }
    for handle in receiver_handles {
        let _ = handle.join();
    }
}

fn performance_test(adapter: &Adapter) -> Result<(), Box<dyn std::error::Error>> {
    const PACKET_COUNT: usize = 10000;

    info!("Performance test with {} packets:", PACKET_COUNT);

    // Test Session performance
    info!("Testing Session performance...");
    let session = adapter.start_session(0x100000)?; // 1MB
    let session = Arc::new(session);

    let start_time = Instant::now();

    // Spawn multiple threads for concurrent operations
    let sender = session.create_sender();
    let receiver = session.create_receiver();
    let session_clone = session.clone();

    let sender_handle = thread::spawn(move || {
        for i in 0..PACKET_COUNT {
            let data = format!("test packet {}", i);
            if let Err(e) = sender.send(data.as_bytes()) {
                error!("Send error: {:?}", e);
                break;
            }

            // Notify receivers periodically
            if i % 100 == 0 {
                session_clone.notify_receivers();
            }
        }
    });

    let receiver_handle = thread::spawn(move || {
        let mut received = 0;
        while received < PACKET_COUNT {
            match receiver.receive_timeout(Duration::from_millis(100)) {
                Ok(_) => received += 1,
                Err(_) => break,
            }
        }
        received
    });

    sender_handle.join().unwrap();
    let received_count = receiver_handle.join().unwrap();

    let elapsed = start_time.elapsed();

    info!("Performance Test Results:");
    info!("  Sent: {} packets", PACKET_COUNT);
    info!("  Received: {} packets", received_count);
    info!("  Time: {:?}", elapsed);
    info!(
        "  Throughput: {:.2} packets/sec",
        PACKET_COUNT as f64 / elapsed.as_secs_f64()
    );

    // Print final session stats
    let stats = session.stats();
    info!("Final session stats: {:?}", stats);

    Ok(())
}

fn create_test_packet() -> Vec<u8> {
    // Create a simple IPv4 packet (ping)
    let mut packet = vec![0u8; 28]; // IP header (20) + ICMP header (8)

    // IPv4 header
    packet[0] = 0x45; // Version 4, IHL 5
    packet[1] = 0x00; // DSCP, ECN
    packet[2..4].copy_from_slice(&28u16.to_be_bytes()); // Total length
    packet[4..6].copy_from_slice(&0x1234u16.to_be_bytes()); // Identification
    packet[6..8].copy_from_slice(&0x4000u16.to_be_bytes()); // Flags, Fragment offset
    packet[8] = 64; // TTL
    packet[9] = 1; // Protocol (ICMP)
    packet[10..12].copy_from_slice(&0u16.to_be_bytes()); // Header checksum (to be calculated)
    packet[12..16].copy_from_slice(&[192, 168, 1, 1]); // Source IP
    packet[16..20].copy_from_slice(&[8, 8, 8, 8]); // Destination IP

    // ICMP header
    packet[20] = 8; // Type (Echo Request)
    packet[21] = 0; // Code
    packet[22..24].copy_from_slice(&0u16.to_be_bytes()); // Checksum (to be calculated)
    packet[24..26].copy_from_slice(&0x1234u16.to_be_bytes()); // Identifier
    packet[26..28].copy_from_slice(&0x0001u16.to_be_bytes()); // Sequence number

    packet
}

fn print_packet_info(data: &[u8]) {
    if data.len() < 20 {
        warn!("  Invalid packet (too short)");
        return;
    }

    let version = (data[0] >> 4) & 0xF;
    if version == 4 {
        let src = &data[12..16];
        let dst = &data[16..20];
        let protocol = data[9];

        info!("  IPv4 packet:");
        info!("    Source: {}.{}.{}.{}", src[0], src[1], src[2], src[3]);
        info!(
            "    Destination: {}.{}.{}.{}",
            dst[0], dst[1], dst[2], dst[3]
        );
        info!("    Protocol: {}", protocol);
    } else {
        info!("  Non-IPv4 packet (version: {})", version);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_library_initialization() {
        velocitun::init();
        assert!(velocitun::version().len() > 0);
    }

    #[test]
    fn test_adapter_builder() {
        let builder = AdapterBuilder::new()
            .name("TestAdapter")
            .tunnel_type("VelociTun");

        // This will fail without admin rights and actual driver
        // but tests the builder pattern
        let _ = builder.build();
    }

    #[test]
    fn test_packet_creation() {
        let data = vec![1, 2, 3, 4, 5];
        let packet = Packet::new(&data).unwrap();

        assert_eq!(packet.data(), &data);
        assert_eq!(packet.size(), 5);
    }

    #[test]
    fn test_packet_ip_parsing() {
        let packet_data = create_test_packet();
        let packet = Packet::new(&packet_data).unwrap();

        assert_eq!(packet.ip_version(), Some(4));
        assert_eq!(packet.source_ipv4(), Some([192, 168, 1, 1]));
        assert_eq!(packet.destination_ipv4(), Some([8, 8, 8, 8]));
        assert_eq!(packet.protocol(), Some(1)); // ICMP
    }

    #[test]
    fn test_session_thread_safety() {
        // Test that Session types implement Send + Sync
        fn is_send_sync<T: Send + Sync>() {}

        is_send_sync::<Session>();
        is_send_sync::<PacketSender>();
        is_send_sync::<PacketReceiver>();
    }
}
