use crate::packet::Packet;
use crate::session::{Session, SessionStats};
use crate::{VelociTunError, VelociTunResult};
use futures::stream::{Stream, StreamExt};
use std::pin::Pin;
use std::sync::Arc;
use std::task::{Context, Poll};
use std::time::Duration;
use tokio::sync::{mpsc, Mutex};
use tokio::time::{sleep, timeout};

/// Async wrapper for Session
pub struct AsyncSession {
    session: Arc<Session>,
    _runtime_handle: Option<tokio::runtime::Handle>,
}

impl AsyncSession {
    /// Create an async session wrapper
    pub fn new(session: Session) -> Self {
        let runtime_handle = tokio::runtime::Handle::try_current().ok();

        Self {
            session: Arc::new(session),
            _runtime_handle: runtime_handle,
        }
    }

    /// Get the underlying session
    pub fn inner(&self) -> &Session {
        &self.session
    }

    /// Send a packet asynchronously
    pub async fn send_packet(&self, data: &[u8]) -> VelociTunResult<()> {
        let session = self.session.clone();
        let data = data.to_vec();

        tokio::task::spawn_blocking(move || session.send_packet(&data))
            .await
            .map_err(|e| VelociTunError::InvalidParameter(e.to_string()))?
    }

    /// Receive a packet asynchronously
    pub async fn receive_packet(&self) -> VelociTunResult<Vec<u8>> {
        let session = self.session.clone();

        tokio::task::spawn_blocking(move || session.receive_packet())
            .await
            .map_err(|e| VelociTunError::InvalidParameter(e.to_string()))?
    }

    /// Try to receive a packet asynchronously (non-blocking)
    pub async fn try_receive_packet(&self) -> VelociTunResult<Option<Vec<u8>>> {
        let session = self.session.clone();

        tokio::task::spawn_blocking(move || session.try_receive_packet())
            .await
            .map_err(|e| VelociTunError::InvalidParameter(e.to_string()))?
    }

    /// Receive a packet with timeout
    pub async fn receive_packet_timeout(
        &self,
        timeout_duration: Duration,
    ) -> VelociTunResult<Vec<u8>> {
        let receive_future = self.receive_packet();

        match timeout(timeout_duration, receive_future).await {
            Ok(result) => result,
            Err(_) => Err(VelociTunError::Timeout),
        }
    }

    /// Get session statistics
    pub fn stats(&self) -> SessionStats {
        self.session.stats()
    }

    /// Get session capacity
    pub fn capacity(&self) -> u32 {
        self.session.capacity()
    }

    /// Create an async packet sender
    pub fn async_packet_sender(&self) -> AsyncPacketSender {
        AsyncPacketSender::new(self.session.clone())
    }

    /// Create an async packet receiver
    pub fn async_packet_receiver(&self) -> AsyncPacketReceiver {
        AsyncPacketReceiver::new(self.session.clone())
    }

    /// Create a packet stream for receiving
    pub fn packet_stream(&self) -> PacketStream {
        PacketStream::new(self.session.clone())
    }

    /// Create a bounded channel for async packet processing
    pub fn create_packet_channel(
        &self,
        buffer_size: usize,
    ) -> (AsyncPacketSender, AsyncPacketReceiver) {
        let (tx, rx) = mpsc::channel(buffer_size);
        let sender = AsyncPacketSender::with_channel(self.session.clone(), tx);
        let receiver = AsyncPacketReceiver::with_channel(self.session.clone(), rx);
        (sender, receiver)
    }
}

/// Async packet sender
pub struct AsyncPacketSender {
    session: Arc<Session>,
    channel: Option<mpsc::Sender<Vec<u8>>>,
}

impl AsyncPacketSender {
    fn new(session: Arc<Session>) -> Self {
        Self {
            session,
            channel: None,
        }
    }

    fn with_channel(session: Arc<Session>, channel: mpsc::Sender<Vec<u8>>) -> Self {
        Self {
            session,
            channel: Some(channel),
        }
    }

    /// Send a packet asynchronously
    pub async fn send(&self, data: &[u8]) -> VelociTunResult<()> {
        if let Some(ref channel) = self.channel {
            // Send through channel
            channel
                .send(data.to_vec())
                .await
                .map_err(|e| VelociTunError::InvalidParameter(e.to_string()))?;
            Ok(())
        } else {
            // Send directly
            let session = self.session.clone();
            let data = data.to_vec();

            tokio::task::spawn_blocking(move || session.send_packet(&data))
                .await
                .map_err(|e| VelociTunError::InvalidParameter(e.to_string()))?
        }
    }

    /// Send multiple packets in batch
    pub async fn send_batch(&self, packets: &[&[u8]]) -> VelociTunResult<()> {
        for packet in packets {
            self.send(packet).await?;
        }
        Ok(())
    }

    /// Check if sender is connected
    pub fn is_connected(&self) -> bool {
        self.channel.as_ref().map_or(true, |ch| !ch.is_closed())
    }
}

/// Async packet receiver
pub struct AsyncPacketReceiver {
    session: Arc<Session>,
    channel: Option<mpsc::Receiver<Vec<u8>>>,
}

impl AsyncPacketReceiver {
    fn new(session: Arc<Session>) -> Self {
        Self {
            session,
            channel: None,
        }
    }

    fn with_channel(session: Arc<Session>, channel: mpsc::Receiver<Vec<u8>>) -> Self {
        Self {
            session,
            channel: Some(channel),
        }
    }

    /// Receive a packet asynchronously
    pub async fn receive(&mut self) -> VelociTunResult<Vec<u8>> {
        if let Some(ref mut channel) = self.channel {
            // Receive from channel
            channel.recv().await.ok_or(VelociTunError::InvalidParameter(
                "Channel closed".to_string(),
            ))
        } else {
            // Receive directly
            let session = self.session.clone();

            tokio::task::spawn_blocking(move || session.receive_packet())
                .await
                .map_err(|e| VelociTunError::InvalidParameter(e.to_string()))?
        }
    }

    /// Try to receive a packet without blocking
    pub async fn try_receive(&mut self) -> VelociTunResult<Option<Vec<u8>>> {
        if let Some(ref mut channel) = self.channel {
            // Try receive from channel
            match channel.try_recv() {
                Ok(data) => Ok(Some(data)),
                Err(mpsc::error::TryRecvError::Empty) => Ok(None),
                Err(mpsc::error::TryRecvError::Disconnected) => Err(
                    VelociTunError::InvalidParameter("Channel closed".to_string()),
                ),
            }
        } else {
            // Try receive directly
            let session = self.session.clone();

            tokio::task::spawn_blocking(move || session.try_receive_packet())
                .await
                .map_err(|e| VelociTunError::InvalidParameter(e.to_string()))?
        }
    }

    /// Receive a packet with timeout
    pub async fn receive_timeout(
        &mut self,
        timeout_duration: Duration,
    ) -> VelociTunResult<Vec<u8>> {
        let receive_future = self.receive();

        match timeout(timeout_duration, receive_future).await {
            Ok(result) => result,
            Err(_) => Err(VelociTunError::Timeout),
        }
    }

    /// Receive multiple packets in batch
    pub async fn receive_batch(&mut self, max_packets: usize) -> VelociTunResult<Vec<Vec<u8>>> {
        let mut packets = Vec::new();

        for _ in 0..max_packets {
            match self.try_receive().await? {
                Some(packet) => packets.push(packet),
                None => break,
            }
        }

        Ok(packets)
    }
}

/// Stream of packets for async iteration
pub struct PacketStream {
    session: Arc<Session>,
    buffer: Option<Vec<u8>>,
}

impl PacketStream {
    fn new(session: Arc<Session>) -> Self {
        Self {
            session,
            buffer: None,
        }
    }
}

impl Stream for PacketStream {
    type Item = VelociTunResult<Vec<u8>>;

    fn poll_next(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        let this = self.get_mut();

        // Try to get a packet from buffer or session
        let session = this.session.clone();
        let mut future = Box::pin(tokio::task::spawn_blocking(move || {
            session.try_receive_packet()
        }));

        match future.as_mut().poll(cx) {
            Poll::Ready(Ok(Ok(Some(packet)))) => Poll::Ready(Some(Ok(packet))),
            Poll::Ready(Ok(Ok(None))) => {
                // No packet available, wake up later
                cx.waker().wake_by_ref();
                Poll::Pending
            }
            Poll::Ready(Ok(Err(e))) => Poll::Ready(Some(Err(e))),
            Poll::Ready(Err(e)) => {
                Poll::Ready(Some(Err(VelociTunError::InvalidParameter(e.to_string()))))
            }
            Poll::Pending => Poll::Pending,
        }
    }
}

/// Async packet processing utilities
pub struct AsyncPacketProcessor {
    session: Arc<Session>,
}

impl AsyncPacketProcessor {
    pub fn new(session: Arc<Session>) -> Self {
        Self { session }
    }

    /// Process packets with async callback
    pub async fn process_packets<F, Fut>(&self, mut callback: F) -> VelociTunResult<()>
    where
        F: FnMut(Packet) -> Fut,
        Fut: std::future::Future<Output = VelociTunResult<()>>,
    {
        let session = self.session.clone();

        loop {
            let packet_data = tokio::task::spawn_blocking({
                let session = session.clone();
                move || session.try_receive_packet()
            })
            .await
            .map_err(|e| VelociTunError::InvalidParameter(e.to_string()))??;

            if let Some(data) = packet_data {
                let packet = Packet::new(&data)?;
                callback(packet).await?;
            } else {
                // No packet available, yield control
                tokio::task::yield_now().await;
            }
        }
    }

    /// Process packets in batches
    pub async fn process_packet_batches<F, Fut>(
        &self,
        batch_size: usize,
        mut callback: F,
    ) -> VelociTunResult<()>
    where
        F: FnMut(Vec<Packet>) -> Fut,
        Fut: std::future::Future<Output = VelociTunResult<()>>,
    {
        let session = self.session.clone();

        loop {
            let mut batch = Vec::new();

            // Collect a batch of packets
            for _ in 0..batch_size {
                let packet_data = tokio::task::spawn_blocking({
                    let session = session.clone();
                    move || session.try_receive_packet()
                })
                .await
                .map_err(|e| VelociTunError::InvalidParameter(e.to_string()))??;

                if let Some(data) = packet_data {
                    batch.push(Packet::new(&data)?);
                } else {
                    break;
                }
            }

            if !batch.is_empty() {
                callback(batch).await?;
            } else {
                // No packets available, yield control
                tokio::task::yield_now().await;
            }
        }
    }
}

impl Clone for AsyncPacketSender {
    fn clone(&self) -> Self {
        Self {
            session: self.session.clone(),
            channel: self.channel.clone(),
        }
    }
}

unsafe impl Send for AsyncSession {}

unsafe impl Sync for AsyncSession {}

unsafe impl Send for AsyncPacketSender {}

unsafe impl Sync for AsyncPacketSender {}

unsafe impl Send for AsyncPacketReceiver {}

unsafe impl Sync for AsyncPacketReceiver {}
