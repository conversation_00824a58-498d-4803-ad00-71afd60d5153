# VelociTun

基于 Rust 语言实现的高性能 TUN 驱动程序，采用现代化的 Rust API 设计，提供类型安全的网络隧道接口。

## 概述

本项目提供了一个现代化、内存安全的 TUN 驱动程序实现，为需要高性能隧道功能的 Windows 应用程序提供第三层网络接口。

## 主要特性

- **类型安全**: 利用 Rust 类型系统防止常见的编程错误
- **Builder 模式**: 灵活的适配器创建方式
- **现代化 API**: 符合 Rust 语言惯例的接口设计
- **资源管理**: 自动的资源清理和生命周期管理
- **线程安全**: 内置的并发安全保证
- **错误处理**: 统一的错误类型和处理机制
- **异步支持**: 完整的 async/await 支持和流处理能力

## 快速开始

### 基本用法

```rust
use velocitun::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    velocitun::init();
    
    // 创建适配器
    let adapter = AdapterBuilder::new()
        .name("MyTunnel")
        .tunnel_type("VelociTun")
        .build()?;
    
    // 启动会话
    let session = adapter.start_session(0x400000)?; // 4MB 缓冲区
    
    // 发送数据包
    let packet_data = vec![/* IP packet data */];
    session.send_packet(&packet_data)?;
    
    // 接收数据包
    match session.try_receive_packet()? {
        Some(data) => println!("收到数据包: {} bytes", data.len()),
        None => println!("没有数据包"),
    }
    
    Ok(())
}
```

### 高级功能

#### 1. 适配器管理

```rust
use velocitun::*;

// 创建带有自定义 GUID 的适配器
let guid = windows::core::GUID::from("550e8400-e29b-41d4-a716-************");
let adapter = AdapterBuilder::new()
    .name("CustomAdapter")
    .guid(guid)
    .build()?;

// 查找现有适配器
let existing = Adapter::find_by_name("ExistingAdapter")?;

// 列出所有适配器
let adapters = Adapter::list_all()?;
for adapter in adapters {
    println!("适配器: {} ({})", adapter.name(), adapter.tunnel_type());
}
```

#### 2. 会话管理

```rust
use velocitun::*;
use std::time::Duration;

let session = adapter.start_session(0x400000)?;

// 阻塞接收
let data = session.receive_packet()?;

// 带超时的接收
let data = session.receive_packet_timeout(Duration::from_secs(5))?;

// 非阻塞接收
if let Some(data) = session.try_receive_packet()? {
    // 处理数据包
}

// 获取会话统计信息
let stats = session.stats();
println!("发送缓冲区使用: {}/{}", stats.send_ring_used, stats.send_ring_available);
```

#### 3. 数据包处理

```rust
use velocitun::*;

// 创建数据包
let mut packet = Packet::new(&raw_data)?;

// 解析 IP 信息
if packet.is_valid_ip() {
    println!("IP 版本: {:?}", packet.ip_version());
    if let Some(src) = packet.source_ipv4() {
        println!("源地址: {}.{}.{}.{}", src[0], src[1], src[2], src[3]);
    }
}

// 修改数据包
packet.append(&additional_data)?;
packet.truncate(new_size);

// 显示数据包信息
println!("数据包信息: {}", packet); // 自动格式化输出
```

#### 4. 多线程使用

```rust
use velocitun::*;
use std::thread;
use std::sync::Arc;

let session = Arc::new(session);

// 发送线程
let sender_session = session.clone();
let sender_handle = thread::spawn(move || {
    let sender = sender_session.packet_sender();
    
    loop {
        if let Err(e) = sender.send(&packet_data) {
            println!("发送失败: {}", e);
            break;
        }
    }
});

// 接收线程
let receiver_session = session.clone();
let receiver_handle = thread::spawn(move || {
    let receiver = receiver_session.packet_receiver();
    
    loop {
        match receiver.receive() {
            Ok(data) => {
                // 处理接收到的数据
            }
            Err(e) => {
                println!("接收失败: {}", e);
                break;
            }
        }
    }
});
```

#### 5. 异步使用

启用异步功能：

```toml
[dependencies]
velocitun = { version = "0.1", features = ["async"] }
tokio = { version = "1.0", features = ["full"] }
```

基本异步操作：

```rust
use velocitun::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化库
    velocitun::init();
    
    // 创建适配器
    let adapter = AdapterBuilder::new()
        .name("AsyncAdapter")
        .tunnel_type("VelociTun")
        .build()?;
    
    // 启动会话
    let session = adapter.start_session(0x400000)?;
    
    // 创建异步会话包装器
    let async_session = AsyncSession::new(session);
    
    // 异步发送数据包
    let packet_data = vec![/* IP packet data */];
    async_session.send_packet(&packet_data).await?;
    
    // 异步接收数据包
    match async_session.try_receive_packet().await? {
        Some(data) => println!("收到数据包: {} bytes", data.len()),
        None => println!("没有数据包"),
    }
    
    // 带超时的异步接收
    use std::time::Duration;
    match async_session.receive_packet_timeout(Duration::from_secs(5)).await {
        Ok(data) => println!("收到数据包: {} bytes", data.len()),
        Err(VelociTunError::Timeout) => println!("接收超时"),
        Err(e) => println!("接收错误: {}", e),
    }
    
    Ok(())
}
```

异步流处理：

```rust
use futures::StreamExt;

// 创建数据包流
let mut stream = async_session.packet_stream();

// 处理数据包流
while let Some(result) = stream.next().await {
    match result {
        Ok(data) => {
            println!("流处理: 收到数据包 {} bytes", data.len());
            // 处理数据包
        }
        Err(e) => println!("流处理错误: {}", e),
    }
}
```

异步通道处理：

```rust
// 创建异步通道
let (sender, mut receiver) = async_session.create_packet_channel(10);

// 发送任务
let send_task = tokio::spawn(async move {
    for i in 0..10 {
        let packet = create_packet(i);
        sender.send(&packet).await?;
    }
    Ok::<(), VelociTunError>(())
});

// 接收任务
let receive_task = tokio::spawn(async move {
    while let Ok(data) = receiver.receive().await {
        println!("接收到数据包: {} bytes", data.len());
    }
});

// 等待任务完成
let (send_result, _) = tokio::join!(send_task, receive_task);
```

批量异步处理：

```rust
use std::sync::Arc;

let session_arc = Arc::new(async_session.inner());
let processor = AsyncPacketProcessor::new(session_arc);

// 批量处理数据包
processor.process_packet_batches(5, |batch| async move {
    println!("处理批量数据包: {} 个", batch.len());
    for packet in batch {
        // 处理每个数据包
        process_packet(packet).await?;
    }
    Ok(())
}).await?;
```

## 异步编程指南

### 异步环境下的使用

在异步环境中，您可以使用 `AsyncSession` 来包装同步的 `Session`，提供完整的异步支持：

```rust
use velocitun::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化（同步）
    velocitun::init();
    
    // 创建适配器和会话（同步）
    let adapter = AdapterBuilder::new()
        .name("AsyncAdapter")
        .tunnel_type("VelociTun")
        .build()?;
    
    let session = adapter.start_session(0x400000)?;
    
    // 创建异步包装器
    let async_session = AsyncSession::new(session);
    
    // 现在可以使用异步 API
    let packet_data = vec![/* 数据包数据 */];
    async_session.send_packet(&packet_data).await?;
    
    Ok(())
}
```

### 异步流处理

使用 `PacketStream` 可以将数据包作为异步流处理：

```rust
use futures::StreamExt;

let mut stream = async_session.packet_stream();

// 使用 while let 处理流
while let Some(result) = stream.next().await {
    match result {
        Ok(packet_data) => {
            println!("收到数据包: {} bytes", packet_data.len());
            // 处理数据包
        }
        Err(e) => println!("流错误: {}", e),
    }
}
```

### 异步通道模式

对于生产者-消费者模式，可以使用异步通道：

```rust
// 创建通道
let (sender, mut receiver) = async_session.create_packet_channel(100);

// 生产者任务
let producer = tokio::spawn(async move {
    for i in 0..1000 {
        let packet = create_packet(i);
        if let Err(e) = sender.send(&packet).await {
            println!("发送失败: {}", e);
            break;
        }
    }
});

// 消费者任务
let consumer = tokio::spawn(async move {
    while let Ok(data) = receiver.receive().await {
        process_packet(data).await;
    }
});

// 等待完成
let (prod_result, cons_result) = tokio::join!(producer, consumer);
```

### 错误处理和超时

异步 API 提供了超时支持，避免无限等待：

```rust
use std::time::Duration;

// 带超时的接收
match async_session.receive_packet_timeout(Duration::from_secs(5)).await {
    Ok(data) => println!("收到数据包"),
    Err(VelociTunError::Timeout) => println!("接收超时"),
    Err(e) => println!("其他错误: {}", e),
}
```

### 性能优化建议

1. **使用适当的缓冲区大小**: 
   ```rust
   let (sender, receiver) = async_session.create_packet_channel(1000); // 根据需要调整
   ```

2. **批量处理**:
   ```rust
   // 批量发送
   let packets: Vec<&[u8]> = packet_list.iter().map(|p| p.as_slice()).collect();
   sender.send_batch(&packets).await?;
   
   // 批量接收
   let mut receiver = async_session.async_packet_receiver();
   let batch = receiver.receive_batch(10).await?;
   ```

3. **并发处理**:
   ```rust
   // 使用多个任务并发处理
   let mut tasks = vec![];
   for i in 0..num_cpus::get() {
       let session = async_session.clone();
       tasks.push(tokio::spawn(async move {
           let mut receiver = session.async_packet_receiver();
           while let Ok(data) = receiver.receive().await {
               process_packet_async(data).await;
           }
       }));
   }
   
   for task in tasks {
       task.await?;
   }
   ```

### 与其他异步库的整合

#### 与 async-std 一起使用

```rust
use async_std::task;

let async_session = AsyncSession::new(session);

task::spawn(async move {
    let mut receiver = async_session.async_packet_receiver();
    while let Ok(data) = receiver.receive().await {
        println!("收到数据包: {} bytes", data.len());
    }
});
```

#### 与 futures 一起使用

```rust
use futures::{future::join_all, stream::StreamExt};

// 创建多个异步任务
let futures: Vec<_> = (0..10).map(|i| {
    let session = async_session.clone();
    async move {
        let sender = session.async_packet_sender();
        let packet = create_packet(i);
        sender.send(&packet).await
    }
}).collect();

// 等待所有任务完成
let results = join_all(futures).await;
```

### 异步模式最佳实践

1. **适当的错误处理**: 始终处理可能的错误情况
2. **资源清理**: 确保异步任务正确释放资源
3. **取消支持**: 使用 `tokio::select!` 或类似机制支持取消
4. **背压控制**: 在高负载情况下控制数据流量

```rust
use tokio::sync::Semaphore;
use std::sync::Arc;

let semaphore = Arc::new(Semaphore::new(100)); // 限制并发数
let mut stream = async_session.packet_stream();

while let Some(result) = stream.next().await {
    if let Ok(data) = result {
        let permit = semaphore.clone().acquire_owned().await?;
        
        tokio::spawn(async move {
            // 处理数据包
            process_packet_async(data).await;
            drop(permit); // 释放许可
        });
    }
}
```

## API 参考

### 核心类型

#### `Adapter`
网络适配器的表示
- `name()` - 获取适配器名称
- `tunnel_type()` - 获取隧道类型
- `guid()` - 获取适配器 GUID
- `luid()` - 获取适配器 LUID
- `start_session(capacity)` - 启动会话

#### `AdapterBuilder`
适配器构建器
- `new()` - 创建新的构建器
- `name(name)` - 设置适配器名称
- `tunnel_type(type)` - 设置隧道类型
- `guid(guid)` - 设置自定义 GUID
- `build()` - 构建适配器

#### `Session`
数据包 I/O 会话
- `send_packet(data)` - 发送数据包
- `receive_packet()` - 接收数据包（阻塞）
- `try_receive_packet()` - 非阻塞接收
- `stats()` - 获取统计信息
- `shutdown()` - 关闭会话

#### `Packet`
网络数据包
- `new(data)` - 创建新数据包
- `data()` - 获取数据
- `size()` - 获取大小
- `is_valid_ip()` - 检查是否为有效 IP 包
- `ip_version()` - 获取 IP 版本
- `source_ipv4()` - 获取 IPv4 源地址
- `destination_ipv4()` - 获取 IPv4 目标地址

### 异步类型

#### `AsyncSession`
异步会话包装器
- `new(session)` - 创建异步会话包装器
- `send_packet(data)` - 异步发送数据包
- `receive_packet()` - 异步接收数据包
- `try_receive_packet()` - 异步非阻塞接收
- `receive_packet_timeout(duration)` - 带超时的异步接收
- `async_packet_sender()` - 创建异步发送器
- `async_packet_receiver()` - 创建异步接收器
- `packet_stream()` - 创建数据包流
- `create_packet_channel(size)` - 创建异步通道

#### `AsyncPacketSender`
异步数据包发送器
- `send(data)` - 异步发送数据包
- `send_batch(packets)` - 批量发送数据包
- `is_connected()` - 检查是否连接

#### `AsyncPacketReceiver`
异步数据包接收器
- `receive()` - 异步接收数据包
- `try_receive()` - 异步非阻塞接收
- `receive_timeout(duration)` - 带超时的异步接收
- `receive_batch(max_packets)` - 批量接收数据包

#### `PacketStream`
数据包流（实现 `Stream` trait）
- `next()` - 获取下一个数据包
- `for_each()` - 遍历所有数据包
- `filter()` - 过滤数据包
- `map()` - 转换数据包

#### `AsyncPacketProcessor`
异步数据包处理器
- `new(session)` - 创建处理器
- `process_packets(callback)` - 处理数据包
- `process_packet_batches(batch_size, callback)` - 批量处理数据包

### 驱动管理

#### `Driver`
驱动程序管理
- `version()` - 获取驱动版本
- `is_available()` - 检查驱动是否可用
- `ensure_available()` - 确保驱动可用
- `info()` - 获取驱动详细信息

### 错误处理

#### `VelociTunError`
统一的错误类型
- `WindowsApi(Error)` - Windows API 错误
- `InvalidParameter(String)` - 参数错误
- `BufferOverflow` - 缓冲区溢出
- `AdapterNotFound` - 适配器未找到
- `DriverNotLoaded` - 驱动未加载
- `Timeout` - 操作超时（异步操作）

## 项目结构

```
src/
├── lib.rs          # 库主入口和便利函数
├── adapter.rs      # 适配器管理和构建器
├── driver.rs       # 驱动程序管理
├── session.rs      # 会话和数据包 I/O
├── packet.rs       # 数据包结构和解析
├── ring.rs         # 环形缓冲区实现
├── resources.rs    # 资源管理（驱动文件）
├── logger.rs       # 日志系统
├── registry.rs     # 注册表操作
├── error.rs        # 错误类型定义
└── async_session.rs # 异步会话支持（可选）
```

## 构建和部署

### 开发环境要求

- Rust 1.70+ 与 MSVC 工具链
- Windows SDK
- 管理员权限（用于驱动安装）

### 构建命令

```bash
# 检查代码
cargo check

# 运行测试
cargo test

# 构建发布版本
cargo build --release

# 运行示例
cargo run --example rust_api

# 运行异步示例
cargo run --example async_api --features async
```

### 集成到项目

在 `Cargo.toml` 中添加依赖：

```toml
[dependencies]
velocitun = "0.1"

# 启用异步功能
velocitun = { version = "0.1", features = ["async"] }
tokio = { version = "1.0", features = ["full"] }
```

## 设计原则

### 1. 类型安全优先
- 使用强类型防止运行时错误
- 编译时检查参数有效性
- 避免空指针和内存泄漏

### 2. 现代化 API 设计
- Builder 模式用于复杂对象创建
- 链式调用提高代码可读性
- Iterator 和 Option 类型的广泛使用

### 3. 资源管理
- RAII 模式自动管理资源
- Drop trait 确保资源正确释放
- Arc/Rc 用于共享资源

### 4. 错误处理
- Result 类型处理所有可能的错误
- 统一的错误类型层次结构
- 详细的错误信息和上下文

### 5. 并发安全
- 原子操作和锁保证线程安全
- Send/Sync trait 标记
- 无锁数据结构优化性能

## 性能优化

### 1. 零拷贝操作
- 直接在环形缓冲区中操作数据
- 避免不必要的内存分配和复制
- 使用 `unsafe` 块进行优化

### 2. 内存对齐
- 所有数据结构按照缓存行对齐
- 减少 false sharing
- 优化内存访问模式

### 3. 批量操作
- 支持批量发送和接收
- 减少系统调用开销
- 优化网络吞吐量

## 故障排除

### 常见问题

1. **驱动安装失败**
   - 确保以管理员权限运行
   - 检查 Windows 版本兼容性
   - 查看系统日志获取详细错误

2. **适配器创建失败**
   - 检查网络适配器权限
   - 确保驱动程序正确安装
   - 验证 GUID 唯一性

3. **数据包发送失败**
   - 检查缓冲区大小
   - 验证数据包格式
   - 确保会话处于活动状态

### 调试技巧

```rust
// 启用详细日志
env_logger::init();

// 检查驱动状态
match Driver::info() {
    Ok(info) => println!("驱动信息: {:?}", info),
    Err(e) => println!("驱动检查失败: {}", e),
}

// 监控会话状态
let stats = session.stats();
println!("会话统计: {:?}", stats);
```

## 贡献指南

欢迎贡献！请确保：

1. 代码遵循 Rust 格式标准 (`cargo fmt`)
2. 所有测试通过 (`cargo test`)
3. 添加适当的文档和示例
4. 遵循现有的 API 设计模式
5. 提供详细的 PR 描述

## 许可证

本项目采用 GPL-2.0 许可证，确保开源兼容性和社区发展。

## 版本历史

- **v0.1.0**: 现代化 Rust API 设计
  - Builder 模式适配器创建
  - 类型安全的会话管理
  - 完整的数据包解析功能
  - 线程安全的并发支持
  - 自动资源管理