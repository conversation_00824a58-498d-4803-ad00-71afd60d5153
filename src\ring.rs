use crate::{VelociTunError, VelociTunResult};
use std::sync::atomic::{AtomicI32, AtomicU32, Ordering};

pub const MIN_RING_CAPACITY: u32 = 0x20000; // 128kiB
pub const MAX_RING_CAPACITY: u32 = 0x4000000; // 64MiB
pub const ALIGNMENT: usize = std::mem::size_of::<u32>();

/// Ring buffer for packet I/O
pub struct Ring {
    head: AtomicU32,
    tail: AtomicU32,
    alertable: AtomicI32,
    capacity: u32,
    data: Vec<u8>,
}

impl Ring {
    pub fn new(capacity: u32) -> VelociTunResult<Self> {
        if !capacity.is_power_of_two() {
            return Err(VelociTunError::InvalidRingCapacity(capacity));
        }

        if !(MIN_RING_CAPACITY..=MAX_RING_CAPACITY).contains(&capacity) {
            return Err(VelociTunError::InvalidRingCapacity(capacity));
        }

        let data_size = capacity as usize + crate::packet::MAX_PACKET_SIZE - ALIGNMENT;
        let data = vec![0u8; data_size];

        Ok(Ring {
            head: AtomicU32::new(0),
            tail: AtomicU32::new(0),
            alertable: AtomicI32::new(0),
            capacity,
            data,
        })
    }

    pub fn capacity(&self) -> u32 {
        self.capacity
    }

    pub fn head(&self) -> u32 {
        self.head.load(Ordering::Acquire)
    }

    pub fn tail(&self) -> u32 {
        self.tail.load(Ordering::Acquire)
    }

    pub fn set_head(&self, value: u32) {
        self.head.store(value, Ordering::Release);
    }

    pub fn set_tail(&self, value: u32) {
        self.tail.store(value, Ordering::Release);
    }

    pub fn is_alertable(&self) -> bool {
        self.alertable.load(Ordering::Acquire) != 0
    }

    pub fn set_alertable(&self, alertable: bool) {
        self.alertable
            .store(if alertable { 1 } else { 0 }, Ordering::Release);
    }

    pub fn wrap_offset(&self, offset: u32) -> u32 {
        offset & (self.capacity - 1)
    }

    pub fn available_space(&self) -> u32 {
        let head = self.head();
        let tail = self.tail();
        self.capacity - (tail.wrapping_sub(head))
    }

    pub fn used_space(&self) -> u32 {
        let head = self.head();
        let tail = self.tail();
        tail.wrapping_sub(head)
    }

    pub fn write_packet(&self, packet: &crate::packet::Packet) -> VelociTunResult<()> {
        let packet_size = packet.total_size() as u32;

        if self.available_space() < packet_size {
            return Err(VelociTunError::BufferOverflow);
        }

        let tail = self.tail();
        let wrapped_tail = self.wrap_offset(tail);

        // Write packet to ring buffer
        let packet_bytes = packet.as_bytes();
        let data_start = wrapped_tail as usize;
        let data_end = data_start + packet_bytes.len();

        if data_end <= self.data.len() {
            // Simple case: packet fits without wrapping
            unsafe {
                let src = packet_bytes.as_ptr();
                let dst = self.data.as_ptr().add(data_start) as *mut u8;
                std::ptr::copy_nonoverlapping(src, dst, packet_bytes.len());
            }
        } else {
            // Wrapped case: split packet across ring boundary
            let first_part_size = self.data.len() - data_start;
            let second_part_size = packet_bytes.len() - first_part_size;

            unsafe {
                // Write first part
                let src = packet_bytes.as_ptr();
                let dst = self.data.as_ptr().add(data_start) as *mut u8;
                std::ptr::copy_nonoverlapping(src, dst, first_part_size);

                // Write second part
                let src = packet_bytes.as_ptr().add(first_part_size);
                let dst = self.data.as_ptr() as *mut u8;
                std::ptr::copy_nonoverlapping(src, dst, second_part_size);
            }
        }

        // Update tail
        self.set_tail(tail.wrapping_add(packet_size));

        Ok(())
    }

    pub fn read_packet(&self) -> VelociTunResult<crate::packet::Packet> {
        let head = self.head();
        let tail = self.tail();

        if head == tail {
            return Err(VelociTunError::NoMoreItems);
        }

        let wrapped_head = self.wrap_offset(head);

        // Read packet size first
        let size_bytes = std::mem::size_of::<u32>();
        let mut size_buffer = [0u8; 4];

        if wrapped_head as usize + size_bytes <= self.data.len() {
            // Size doesn't wrap
            unsafe {
                let src = self.data.as_ptr().add(wrapped_head as usize);
                std::ptr::copy_nonoverlapping(src, size_buffer.as_mut_ptr(), size_bytes);
            }
        } else {
            // Size wraps around
            let first_part = self.data.len() - wrapped_head as usize;
            let second_part = size_bytes - first_part;

            unsafe {
                let src = self.data.as_ptr().add(wrapped_head as usize);
                std::ptr::copy_nonoverlapping(src, size_buffer.as_mut_ptr(), first_part);

                let src = self.data.as_ptr();
                std::ptr::copy_nonoverlapping(
                    src,
                    size_buffer.as_mut_ptr().add(first_part),
                    second_part,
                );
            }
        }

        let packet_size = u32::from_le_bytes(size_buffer);

        if packet_size > crate::packet::MAX_IP_PACKET_SIZE as u32 {
            return Err(VelociTunError::InvalidData);
        }

        let total_packet_size =
            crate::packet::align_up(size_bytes + packet_size as usize, ALIGNMENT);
        let mut packet_buffer = vec![0u8; total_packet_size];

        // Read entire packet
        let data_start = wrapped_head as usize;
        let data_end = data_start + total_packet_size;

        if data_end <= self.data.len() {
            // Simple case: packet doesn't wrap
            unsafe {
                let src = self.data.as_ptr().add(data_start);
                std::ptr::copy_nonoverlapping(src, packet_buffer.as_mut_ptr(), total_packet_size);
            }
        } else {
            // Wrapped case
            let first_part_size = self.data.len() - data_start;
            let second_part_size = total_packet_size - first_part_size;

            unsafe {
                let src = self.data.as_ptr().add(data_start);
                std::ptr::copy_nonoverlapping(src, packet_buffer.as_mut_ptr(), first_part_size);

                let src = self.data.as_ptr();
                std::ptr::copy_nonoverlapping(
                    src,
                    packet_buffer.as_mut_ptr().add(first_part_size),
                    second_part_size,
                );
            }
        }

        // Update head
        self.set_head(head.wrapping_add(total_packet_size as u32));

        // Create packet from buffer
        let packet = crate::packet::Packet::from_bytes(&packet_buffer)?;

        Ok(packet)
    }
}
