# VelociTun

A high-performance TUN driver for Windows built with Rust, providing fast and secure network tunneling capabilities.

## Overview

This project provides a modern, memory-safe TUN driver implementation for Windows using Rust. It offers a layer 3 network interface for applications requiring high-performance tunneling capabilities.

## Features

- **Memory Safe**: Leverages Rust's ownership model for safer memory management
- **WDF Integration**: Built on Windows Driver Framework for robust driver development
- **API Compatible**: Maintains compatibility with existing TUN applications
- **Cross-Architecture**: Supports x86_64 Windows systems
- **Modern Tooling**: Uses Cargo for dependency management and builds

## Project Structure

```
src/
├── lib.rs          # Main library interface
├── adapter.rs      # Network adapter management
├── driver.rs       # Driver lifecycle and IOCTL handling
├── session.rs      # Session management and packet I/O
├── packet.rs       # Packet structure and utilities
├── ring.rs         # Ring buffer implementation
├── logger.rs       # Logging infrastructure
├── registry.rs     # Windows registry operations
└── error.rs        # Error handling and types
```

## Building

### Prerequisites

- Rust 1.70+ with MSVC toolchain
- Windows SDK
- Windows Driver Kit (WDK) for full driver functionality

### Build Commands

```bash
# Debug build
cargo build

# Release build
cargo build --release
```

## Usage

The Rust port provides a modern TUN interface while maintaining compatibility:

```rust
use velocitun::*;

// Create adapter
let adapter = VelociTunCreateAdapter(
    name.as_ptr(),
    tunnel_type.as_ptr(), 
    &guid
);

// Start session
let session = VelociTunStartSession(adapter, capacity);

// Allocate and send packet
let packet = VelociTunAllocateSendPacket(session, size);
// ... fill packet data ...
VelociTunSendPacket(session, packet);

// Receive packet
let mut packet_size = 0;
let packet = VelociTunReceivePacket(session, &mut packet_size);
// ... process packet ...
VelociTunReleaseReceivePacket(session, packet);
```

## Architecture

### Core Components

1. **Adapter Management** (`adapter.rs`)
   - Network interface creation and configuration
   - GUID-based adapter identification
   - Registry integration for persistence

2. **Session Handling** (`session.rs`)
   - Ring buffer allocation and management
   - Thread-safe packet operations
   - Event-driven I/O notifications

3. **Driver Interface** (`driver.rs`)
   - IOCTL command processing
   - Driver lifecycle management
   - Version compatibility checking

4. **Memory Management** (`ring.rs`, `packet.rs`)
   - Lock-free ring buffer implementation
   - Aligned memory allocation
   - Zero-copy packet handling

### Safety Guarantees

- **Memory Safety**: Rust's ownership prevents use-after-free and buffer overflows
- **Thread Safety**: Atomic operations and proper synchronization primitives
- **Resource Management**: RAII ensures proper cleanup of handles and memory
- **Type Safety**: Strong typing prevents many categories of runtime errors

## Configuration

Build configuration is managed through `Cargo.toml`:

```toml
[package.metadata.velocitun]
driver-version = "0.14.1"
minimum-os-version = "6.1" # Windows 7
```

## License

This project is licensed under GPL-2.0, ensuring open-source compatibility and community development.

## Development Status

This is a high-performance TUN driver implementation built with Rust to demonstrate modern system programming capabilities. It serves as:

- A reference implementation for Rust in Windows driver development
- A foundation for memory-safe network driver development
- An example of C-to-Rust porting best practices

## Contributing

Contributions welcome! Please ensure:

- Code follows Rust formatting standards (`cargo fmt`)
- All tests pass (`cargo test`)
- Documentation is updated for new features
- Windows-specific testing on target platforms