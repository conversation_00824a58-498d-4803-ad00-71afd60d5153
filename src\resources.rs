use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>unR<PERSON><PERSON>};
use std::path::Path;
use windows::Win32::Foundation::*;

// Embedded VelociTun driver resources - these are the actual binary files
// generated by build.rs and embedded via include_bytes! for distribution

// AMD64 resources (built by default)
#[cfg(target_arch = "x86_64")]
static VELOCITUN_INF_AMD64: &[u8] = include_bytes!("../resources/velocitun-amd64.inf");
#[cfg(target_arch = "x86_64")]
static VELOCITUN_SYS_AMD64: &[u8] = include_bytes!("../resources/velocitun-amd64.sys");
#[cfg(target_arch = "x86_64")]
static VELOCITUN_CAT_AMD64: &[u8] = include_bytes!("../resources/velocitun-amd64.cat");

// ARM64 resources (optional - only if built)
#[cfg(target_arch = "aarch64")]
static VELOCITUN_INF_ARM64: &[u8] = include_bytes!("../resources/velocitun-arm64.inf");
#[cfg(target_arch = "aarch64")]
static VELOCITUN_SYS_ARM64: &[u8] = include_bytes!("../resources/velocitun-arm64.sys");
#[cfg(target_arch = "aarch64")]
static VELOCITUN_CAT_ARM64: &[u8] = include_bytes!("../resources/velocitun-arm64.cat");

pub struct ResourceManager;

impl ResourceManager {
    /// Extract driver files from embedded resources to specified paths
    pub fn extract_driver_files(
        inf_path: &Path,
        sys_path: &Path,
        cat_path: &Path,
    ) -> VelociTunResult<()> {
        let architecture = Self::get_current_architecture()?;

        crate::logger::log_info!(
            "Extracting VelociTun driver files for architecture: {}",
            architecture
        );
        crate::logger::log_info!("INF: {:?}", inf_path);
        crate::logger::log_info!("SYS: {:?}", sys_path);
        crate::logger::log_info!("CAT: {:?}", cat_path);

        match architecture.as_str() {
            "amd64" => {
                std::fs::write(inf_path, VELOCITUN_INF_AMD64).map_err(|_| {
                    VelociTunError::WindowsApi(windows::core::Error::from(ERROR_WRITE_FAULT))
                })?;
                std::fs::write(sys_path, VELOCITUN_SYS_AMD64).map_err(|_| {
                    VelociTunError::WindowsApi(windows::core::Error::from(ERROR_WRITE_FAULT))
                })?;
                std::fs::write(cat_path, VELOCITUN_CAT_AMD64).map_err(|_| {
                    VelociTunError::WindowsApi(windows::core::Error::from(ERROR_WRITE_FAULT))
                })?;
            }
            "arm64" => {
                #[cfg(feature = "arm64-driver")]
                {
                    std::fs::write(inf_path, VELOCITUN_INF_ARM64).map_err(|_| {
                        VelociTunError::WindowsApi(windows::core::Error::from(ERROR_WRITE_FAULT))
                    })?;
                    std::fs::write(sys_path, VELOCITUN_SYS_ARM64).map_err(|_| {
                        VelociTunError::WindowsApi(windows::core::Error::from(ERROR_WRITE_FAULT))
                    })?;
                    std::fs::write(cat_path, VELOCITUN_CAT_ARM64).map_err(|_| {
                        VelociTunError::WindowsApi(windows::core::Error::from(ERROR_WRITE_FAULT))
                    })?;
                }
                #[cfg(not(feature = "arm64-driver"))]
                {
                    return Err(VelociTunError::InvalidParameter(
                        "ARM64 driver not available. Build with --features arm64-driver"
                            .to_string(),
                    ));
                }
            }
            _ => {
                return Err(VelociTunError::InvalidParameter(format!(
                    "Unsupported architecture: {}",
                    architecture
                )));
            }
        }

        Ok(())
    }

    /// Get current system architecture
    #[allow(non_snake_case)]
    fn get_current_architecture() -> VelociTunResult<String> {
        use windows::Win32::System::SystemInformation::*;

        unsafe {
            let mut system_info = SYSTEM_INFO::default();
            GetNativeSystemInfo(&mut system_info);

            match system_info.Anonymous.Anonymous.wProcessorArchitecture {
                PROCESSOR_ARCHITECTURE_AMD64 => Ok("amd64".to_string()),
                PROCESSOR_ARCHITECTURE_ARM64 => Ok("arm64".to_string()),
                PROCESSOR_ARCHITECTURE_INTEL => Ok("x86".to_string()),
                _ => Err(VelociTunError::InvalidParameter(
                    "Unknown processor architecture".to_string(),
                )),
            }
        }
    }

    /// Get the expected driver file path in system directory
    pub fn get_system_driver_path() -> VelociTunResult<std::path::PathBuf> {
        use windows::Win32::System::SystemInformation::GetSystemDirectoryW;

        unsafe {
            let mut buffer = vec![0u16; 260]; // MAX_PATH
            let length = GetSystemDirectoryW(Some(&mut buffer));

            if length == 0 {
                return Err(VelociTunError::WindowsApi(
                    windows::core::Error::from_win32(),
                ));
            }

            buffer.truncate(length as usize);
            let system_dir = String::from_utf16_lossy(&buffer);
            Ok(std::path::PathBuf::from(system_dir)
                .join("drivers")
                .join("velocitun.sys"))
        }
    }

    /// Create a temporary directory for driver extraction
    pub fn create_temp_driver_directory() -> VelociTunResult<std::path::PathBuf> {
        let temp_dir = std::env::temp_dir();
        let random_name = uuid::Uuid::new_v4().to_string();
        let driver_temp_dir = temp_dir.join(format!("velocitun_{}", random_name));

        std::fs::create_dir_all(&driver_temp_dir).map_err(|_| {
            VelociTunError::WindowsApi(windows::core::Error::from(ERROR_ACCESS_DENIED))
        })?;

        Ok(driver_temp_dir)
    }

    /// Clean up temporary driver files
    pub fn cleanup_temp_files(temp_dir: &std::path::Path) -> VelociTunResult<()> {
        if temp_dir.exists() {
            std::fs::remove_dir_all(temp_dir).map_err(|_| {
                VelociTunError::WindowsApi(windows::core::Error::from(ERROR_ACCESS_DENIED))
            })?;
        }
        Ok(())
    }
}
